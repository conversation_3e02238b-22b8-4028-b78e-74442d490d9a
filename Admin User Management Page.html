<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý tài khoản (Admin)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Roboto', sans-serif; background-color: #f8f9fa; }
        .admin-container { display: flex; min-height: 100vh; }
        .sidebar { width: 250px; background-color: #fff; border-right: 1px solid #dee2e6; padding: 20px; }
        .sidebar .nav-link { color: #495057; padding: 10px; border-radius: 5px; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { background-color: #e9ecef; color: #007bff; }
        .main-content { flex: 1; padding: 20px; }
        .content-section { background: #fff; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); padding: 20px; }
        .btn-primary { background-color: #007bff; border: none; }
        .btn-secondary { background-color: #6c757d; border: none; }
        .btn-success { background-color: #28a745; border: none; }
        .btn-danger { background-color: #dc3545; border: none; }
        .btn-warning { background-color: #ffc107; border: none; }
        .toolbar { display: flex; gap: 10px; margin-bottom: 10px; padding: 5px; border: 1px solid #dee2e6; border-radius: 5px; background-color: #f8f9fa; }
        .toolbar button { background: none; border: none; color: #495057; }
        .toolbar button:hover { color: #007bff; }
        .input-field-user-info { max-width: 50% !important; }
        .pagination { margin-top: 10px; }
        .verification-input { display: none; margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }
        .verification-input.active { display: block; }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="sidebar">
            <h5>Quản lý hệ thống (Admin)</h5>
            <ul class="nav flex-column">
                <li class="nav-item"><a class="nav-link active" data-section="user-list">Quản lý tài khoản</a></li>
                <li class="nav-item"><a class="nav-link" data-section="email-template-management">Template</a></li>
                <li class="nav-item"><a class="nav-link" data-section="sent-emails">Thư đã gửi</a></li>
                <li class="nav-item"><a class="nav-link" data-section="ip-pool-management">IP Pool</a></li>
                <li class="nav-item"><a class="nav-link" data-section="worker-management">Quản lý Worker</a></li>
                <li class="nav-item"><a class="nav-link" data-section="domain-management">Quản lý tên miền</a></li>
                <li class="nav-item"><a class="nav-link" data-section="mta-management">Quản lý MTA</a></li>
                <li class="nav-item"><a class="nav-link" data-section="mta-relay">MTA Relay</a></li>
                <li class="nav-item"><a class="nav-link" data-section="dns">DNS</a></li>
                <li class="nav-item"><a class="nav-link" data-section="contact-management">Quản lý contact</a></li>
                <li class="nav-item"><a class="nav-link" data-section="report">Báo cáo</a></li>
            </ul>
        </div>
        <div class="main-content">
            <div class="content-section" id="user-list-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Danh sách tài khoản</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="searchUser" placeholder="Tìm kiếm người dùng" oninput="searchUsers()" style="width: 200px;">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">Tạo mới tài khoản</button>
                    </div>
                </div>
                <table class="table table-bordered" id="user-table">
                    <thead><tr><th>Họ tên</th><th>Email</th><th>Vai trò</th><th>Trạng thái</th><th>Hành động</th></tr></thead>
                    <tbody id="user-list"></tbody>
                </table>
                <nav aria-label="Page navigation"><ul class="pagination" id="user-pagination"></ul></nav>
            </div>
            <div class="content-section d-none" id="email-template-management-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Template</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="searchTemplate" placeholder="Tìm kiếm template" oninput="searchTemplates()" style="width: 200px;">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">Tạo mới template</button>
                    </div>
                </div>
                <table class="table table-bordered">
                    <thead><tr><th>Tên template</th><th>Trạng thái</th><th>Ngày tạo</th><th>Hành động</th></tr></thead>
                    <tbody id="template-list"></tbody>
                </table>
                <nav aria-label="Page navigation"><ul class="pagination" id="template-pagination"></ul></nav>
            </div>
            <div class="content-section d-none" id="sent-emails-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Thư đã gửi</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="searchSentEmails" placeholder="Tìm kiếm thư đã gửi" oninput="searchSentEmails()" style="width: 200px;">
                    </div>
                </div>
                <table class="table table-bordered">
                    <thead><tr><th>Tiêu đề</th><th>Người nhận</th><th>Ngày gửi</th><th>Trạng thái</th><th>Hành động</th></tr></thead>
                    <tbody id="sent-emails-list"></tbody>
                </table>
                <nav aria-label="Page navigation"><ul class="pagination" id="sent-emails-pagination"></ul></nav>
            </div>
            <div class="content-section d-none" id="view-sent-email-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Chi tiết thư đã gửi: <span id="sentEmailSubject"></span></h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-warning" onclick="editSentEmail()">Chỉnh sửa và gửi lại</button>
                        <button class="btn btn-secondary" onclick="switchSection('sent-emails')">Quay lại</button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><strong>Người gửi:</strong></label>
                            <p id="sentEmailSender"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>Người nhận:</strong></label>
                            <p id="sentEmailRecipient"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>Ngày gửi:</strong></label>
                            <p id="sentEmailDate"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>Trạng thái:</strong></label>
                            <p id="sentEmailStatus"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><strong>Template sử dụng:</strong></label>
                            <p id="sentEmailTemplate"></p>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>Nội dung email:</strong></label>
                    <div class="border p-3" id="sentEmailContent" style="min-height: 200px; background-color: #f8f9fa;"></div>
                </div>
            </div>
            <div class="content-section d-none" id="ip-pool-management-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Quản lý IP Pool</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="searchIpPool" placeholder="Tìm kiếm IP Pool" oninput="searchIpPools()" style="width: 200px;">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createIpPoolModal">Tạo mới IP Pool</button>
                    </div>
                </div>
                <table class="table table-bordered" id="ip-pool-table">
                    <thead><tr><th>Tên IP Pool</th><th>Số lượng IP</th><th>Hành động</th></tr></thead>
                    <tbody id="ip-pool-list"></tbody>
                </table>
                <nav aria-label="Page navigation"><ul class="pagination" id="ip-pool-pagination"></ul></nav>
            </div>
            <div class="content-section d-none" id="ip-pool-detail-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Chi tiết IP Pool: <span id="ipPoolName"></span></h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addIpModal">Thêm IP mới</button>
                        <button class="btn btn-secondary" onclick="switchSection('ip-pool-management')">Quay lại</button>
                    </div>
                </div>
                <table class="table table-bordered" id="ip-list-table">
                    <thead><tr><th>Địa chỉ IP</th><th>Hostname</th><th>Mức độ ưu tiên</th><th>Mô tả</th><th>Hành động</th></tr></thead>
                    <tbody id="ip-list"></tbody>
                </table>
            </div>
            <div class="content-section d-none" id="worker-management-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Quản lý Worker</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="searchWorker" placeholder="Tìm kiếm Worker" oninput="searchWorkers()" style="width: 200px;">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createWorkerModal">Tạo mới Worker</button>
                    </div>
                </div>
                <table class="table table-bordered" id="worker-table">
                    <thead><tr><th>Tên Worker</th><th>Tốc độ gửi thư (email/phút)</th><th>IP Pool</th><th>Hành động</th></tr></thead>
                    <tbody id="worker-list"></tbody>
                </table>
                <nav aria-label="Page navigation"><ul class="pagination" id="worker-pagination"></ul></nav>
            </div>
            <div class="content-section d-none" id="worker-detail-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Chỉnh sửa Worker: <span id="workerName"></span></h5>
                    <button class="btn btn-secondary" onclick="switchSection('worker-management')">Quay lại</button>
                </div>
                <div class="mb-3"><label for="editWorkerName" class="form-label">Tên Worker</label><input type="text" class="form-control" id="editWorkerName"></div>
                <div class="mb-3"><label for="editWorkerSpeed" class="form-label">Tốc độ gửi thư (email/phút)</label><input type="number" class="form-control" id="editWorkerSpeed"></div>
                <div class="mb-3"><label for="editWorkerIpPool" class="form-label">IP Pool</label><select class="form-control" id="editWorkerIpPool"><option value="">-- Chọn IP Pool --</option></select></div>
                <div class="d-flex gap-2"><button class="btn btn-success" onclick="saveWorker()">Lưu thay đổi</button><button class="btn btn-secondary" onclick="switchSection('worker-management')">Hủy</button></div>
            </div>
            <div class="content-section d-none" id="domain-management-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Quản lý tên miền</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="searchDomain" placeholder="Tìm kiếm tên miền" oninput="searchDomains()" style="width: 200px;">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createDomainModal">Tạo mới tên miền</button>
                    </div>
                </div>
                <table class="table table-bordered" id="domain-table">
                    <thead><tr><th>Tên miền</th><th>Trạng thái</th><th>Hành động</th></tr></thead>
                    <tbody id="domain-list"></tbody>
                </table>
                <nav aria-label="Page navigation"><ul class="pagination" id="domain-pagination"></ul></nav>
            </div>
            <div class="content-section d-none" id="domain-detail-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Chi tiết tên miền: <span id="detailDomainName"></span></h5>
                    <button class="btn btn-secondary" onclick="switchSection('domain-management')">Quay lại</button>
                </div>
                <div class="mb-3">
                    <h6>Thông tin xác thực</h6>
                    <table class="table table-bordered">
                        <thead><tr><th>Trạng thái</th><th>Mã xác thực</th></tr></thead>
                        <tbody>
                            <tr><td id="detailDomainStatus"></td><td id="detailVerificationCode"></td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="content-section d-none" id="domain-verification-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Hướng dẫn xác thực tên miền: <span id="verifyDomainName"></span></h5>
                    <button class="btn btn-secondary" onclick="switchSection('domain-management')">Quay lại</button>
                </div>
                <p>Để xác thực tên miền, hãy thêm các bản ghi DNS sau vào cài đặt DNS của bạn:</p>
                <table class="table table-bordered">
                    <thead><tr><th>Loại bản ghi</th><th>Tên</th><th>Giá trị</th><th>TTL</th></tr></thead>
                    <tbody>
                        <tr><td>TXT</td><td>_acme-challenge</td><td><span id="verificationCode"></span></td><td>3600</td></tr>
                    </tbody>
                </table>
                <p>Phương pháp 1: Nhập thông tin bạn đã cấu hình để xác thực:</p>
                <div class="mb-3"><label for="dnsRecordType" class="form-label">Loại bản ghi</label><input type="text" class="form-control" id="dnsRecordType" placeholder="Nhập loại bản ghi (ví dụ: TXT)"></div>
                <div class="mb-3"><label for="dnsRecordName" class="form-label">Tên</label><input type="text" class="form-control" id="dnsRecordName" placeholder="(ví dụ: _acme-challenge hoặc @)"></div>
                <div class="mb-3"><label for="dnsRecordValue" class="form-label">Giá trị</label><input type="text" class="form-control" id="dnsRecordValue" placeholder="Nhập giá trị"></div>
                <div class="mb-3"><label for="dnsRecordTTL" class="form-label">TTL</label><input type="number" class="form-control" id="dnsRecordTTL" placeholder="Nhập TTL (ví dụ: 3600)"></div>
                <button class="btn btn-primary mb-3" onclick="verifyDomainCheck()">Kiểm tra xác thực</button>
                <p>Phương pháp 2: Tải file xác thực lên (file .txt chứa mã xác thực):</p>
                <div class="mb-3"><label for="verificationFile" class="form-label">Tải file lên</label><input type="file" class="form-control" id="verificationFile" accept=".txt"></div>
                <button class="btn btn-primary" onclick="verifyDomainWithFile()">Xác thực bằng file</button>
            </div>
            <div class="content-section d-none" id="mta-management-section"><h5>Quản lý MTA</h5><p>Chỗ dành sẵn cho nội dung Quản lý MTA.</p></div>
            <div class="content-section d-none" id="mta-relay-section"><h5>MTA Relay</h5><p>Chỗ dành sẵn cho nội dung MTA Relay.</p></div>
            <div class="content-section d-none" id="dns-section"><h5>DNS</h5><p>Chỗ dành sẵn cho nội dung DNS.</p></div>
            <div class="content-section d-none" id="report-section"><h5>Báo cáo</h5><p>Chỗ dành sẵn cho nội dung Báo cáo.</p></div>
            <div class="content-section d-none" id="contact-management-section"><h5>Quản lý contact</h5><p>Chỗ dành sẵn cho nội dung Quản lý contact.</p></div>
            <div class="content-section d-none" id="edit-user-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Chỉnh sửa tài khoản: <span id="editUserId"></span></h5>
                    <button class="btn btn-secondary" onclick="switchSection('user-list')">Quay lại</button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="editUserFullName" class="form-label">Họ tên</label>
                            <input type="text" class="form-control input-field-user-info" id="editUserFullName">
                        </div>
                        <div class="mb-3">
                            <label for="editUserEmail" class="form-label">Email</label>
                            <input type="email" class="form-control input-field-user-info" id="editUserEmail">
                        </div>
                        <div class="mb-3">
                            <label for="editUserRole" class="form-label">Vai trò</label>
                            <select class="form-control input-field-user-info" id="editUserRole">
                                <option value="User">User</option>
                                <option value="Admin">Admin</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editUserStatus" class="form-label">Trạng thái</label>
                            <select class="form-control input-field-user-info" id="editUserStatus">
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="saveUserChanges()">Lưu thay đổi</button>
                    <button class="btn btn-secondary" onclick="switchSection('user-list')">Hủy</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="createUserModal" tabindex="-1" aria-labelledby="createUserModalLabel" aria-hidden="true">
        <div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="createUserModalLabel">Tạo mới tài khoản</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><div class="mb-3"><label for="createFullName" class="form-label">Họ tên</label><input type="text" class="form-control" id="createFullName" placeholder="Nhập họ tên"></div><div class="mb-3"><label for="createEmail" class="form-label">Email</label><input type="email" class="form-control" id="createEmail" placeholder="Nhập email"></div><div class="mb-3"><label for="createPassword" class="form-label">Mật khẩu</label><input type="password" class="form-control" id="createPassword"></div></div><div class="modal-footer"><button type="button" class="btn btn-primary" onclick="createUser()">Tạo tài khoản</button><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button></div></div></div>
    </div>
    <div class="modal fade" id="createIpPoolModal" tabindex="-1" aria-labelledby="createIpPoolModalLabel" aria-hidden="true">
        <div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="createIpPoolModalLabel">Tạo mới IP Pool</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><div class="mb-3"><label for="ipPoolNameInput" class="form-label">Tên IP Pool</label><input type="text" class="form-control" id="ipPoolNameInput" placeholder="Nhập tên IP Pool"></div></div><div class="modal-footer"><button type="button" class="btn btn-primary" onclick="createIpPool()">Tạo</button><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button></div></div></div>
    </div>
    <div class="modal fade" id="addIpModal" tabindex="-1" aria-labelledby="addIpModalLabel" aria-hidden="true">
        <div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="addIpModalLabel">Thêm IP mới</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><div class="mb-3"><label for="ipAddressInput" class="form-label">Địa chỉ IP</label><input type="text" class="form-control" id="ipAddressInput" placeholder="Nhập địa chỉ IP (VD: ***********)"></div></div><div class="modal-footer"><button type="button" class="btn btn-primary" onclick="addIpToPool()">Thêm</button><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button></div></div></div>
    </div>
    <div class="modal fade" id="createWorkerModal" tabindex="-1" aria-labelledby="createWorkerModalLabel" aria-hidden="true">
        <div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="createWorkerModalLabel">Tạo mới Worker</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><div class="mb-3"><label for="workerNameInput" class="form-label">Tên Worker</label><input type="text" class="form-control" id="workerNameInput" placeholder="Nhập tên Worker"></div><div class="mb-3"><label for="workerSpeedInput" class="form-label">Tốc độ gửi thư (email/phút)</label><input type="number" class="form-control" id="workerSpeedInput" placeholder="Nhập tốc độ (VD: 100)"></div></div><div class="modal-footer"><button type="button" class="btn btn-primary" onclick="createWorker()">Tạo</button><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button></div></div></div>
    </div>
    <div class="modal fade" id="createDomainModal" tabindex="-1" aria-labelledby="createDomainModalLabel" aria-hidden="true">
        <div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="createDomainModalLabel">Tạo mới tên miền</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><div class="mb-3"><label for="domainNameInput" class="form-label">Tên miền</label><input type="text" class="form-control" id="domainNameInput" placeholder="Nhập tên miền (VD: example.com)"></div></div><div class="modal-footer"><button type="button" class="btn btn-primary" onclick="createDomain()">Tạo</button><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button></div></div></div>
    </div>
    <div class="modal fade" id="createTemplateModal" tabindex="-1" aria-labelledby="createTemplateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createTemplateModalLabel">Tạo mới email template</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="templateName" class="form-label">Tên template</label>
                        <input type="text" class="form-control" id="templateName" placeholder="Nhập tên template">
                    </div>
                    <div class="mb-3">
                        <label for="templateSubject" class="form-label">Tiêu đề email</label>
                        <input type="text" class="form-control" id="templateSubject" placeholder="Nhập tiêu đề email">
                    </div>
                    <div class="mb-3">
                        <label for="templateContent" class="form-label">Nội dung template (HTML)</label>
                        <textarea class="form-control" id="templateContent" rows="10" placeholder="Nhập nội dung HTML"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="saveTemplate()">Lưu</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="editTemplateModal" tabindex="-1" aria-labelledby="editTemplateModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTemplateModalLabel">Chỉnh sửa template</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editTemplateName" class="form-label">Tên template</label>
                        <input type="text" class="form-control" id="editTemplateName" placeholder="Nhập tên template">
                    </div>
                    <div class="mb-3">
                        <label for="editTemplateSubject" class="form-label">Tiêu đề email</label>
                        <input type="text" class="form-control" id="editTemplateSubject" placeholder="Nhập tiêu đề email">
                    </div>
                    <div class="mb-3">
                        <label for="editTemplateContent" class="form-label">Nội dung template (HTML)</label>
                        <textarea class="form-control" id="editTemplateContent" rows="10" placeholder="Nhập nội dung HTML"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" onclick="updateTemplate()">Cập nhật</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="sendEmailModal" tabindex="-1" aria-labelledby="sendEmailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sendEmailModalLabel">Gửi email</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="sendEmailTemplate" class="form-label">Template</label>
                        <select class="form-control" id="sendEmailTemplate" onchange="loadTemplateForSending()">
                            <option value="">-- Chọn template --</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="sendEmailRecipient" class="form-label">Người nhận</label>
                        <input type="email" class="form-control" id="sendEmailRecipient" placeholder="Nhập email người nhận">
                    </div>
                    <div class="mb-3">
                        <label for="sendEmailSubject" class="form-label">Tiêu đề</label>
                        <input type="text" class="form-control" id="sendEmailSubject" placeholder="Tiêu đề email">
                    </div>
                    <div class="mb-3">
                        <label for="sendEmailContent" class="form-label">Nội dung email</label>
                        <textarea class="form-control" id="sendEmailContent" rows="10" placeholder="Nội dung email"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="sendEmail()">Gửi email</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function switchSection(section) {
            document.querySelectorAll('.main-content .content-section').forEach(sec => sec.classList.add('d-none'));
            document.querySelectorAll('.sidebar .nav-link').forEach(link => link.classList.remove('active'));
            document.getElementById(`${section}-section`).classList.remove('d-none');
            document.querySelector(`.nav-link[data-section="${section}"]`).classList.add('active');
        }

        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', () => {
                const section = link.getAttribute('data-section');
                switchSection(section);
                if (section === 'user-list') renderUsers(1);
                else if (section === 'email-template-management') renderTemplates(1);
                else if (section === 'sent-emails') renderSentEmails(1);
                else if (section === 'ip-pool-management') renderIpPools(1);
                else if (section === 'worker-management') renderWorkers(1);
                else if (section === 'domain-management') renderDomains(1);
            });
        });

        let users = { user1: { fullName: "Nguyễn Văn A", email: "<EMAIL>", role: "User", status: "Active" }, user2: { fullName: "Trần Thị B", email: "<EMAIL>", role: "Admin", status: "Inactive" } };
        let templates = {
            template1: {
                name: "Template Chào mừng",
                subject: "Chào mừng bạn đến với hệ thống",
                content: "<h2>Chào mừng!</h2><p>Cảm ơn bạn đã tham gia hệ thống của chúng tôi.</p>",
                status: "Active",
                createdDate: "2024-01-15"
            },
            template2: {
                name: "Template Khuyến mãi",
                subject: "Ưu đãi đặc biệt dành cho bạn",
                content: "<h2>Ưu đãi đặc biệt!</h2><p>Giảm giá 50% cho tất cả sản phẩm trong tháng này.</p>",
                status: "Active",
                createdDate: "2024-01-20"
            },
            template3: {
                name: "Template Thông báo",
                subject: "Thông báo quan trọng",
                content: "<h2>Thông báo</h2><p>Hệ thống sẽ bảo trì vào ngày mai từ 2h-4h sáng.</p>",
                status: "Bản nháp",
                createdDate: "2024-01-25"
            }
        };
        let sentEmails = {
            email1: {
                subject: "Chào mừng bạn đến với hệ thống",
                recipient: "<EMAIL>",
                sender: "<EMAIL>",
                sentDate: "2024-01-16 10:30:00",
                status: "Đã gửi",
                template: "Template Chào mừng",
                content: "<h2>Chào mừng!</h2><p>Cảm ơn bạn đã tham gia hệ thống của chúng tôi.</p>"
            },
            email2: {
                subject: "Ưu đãi đặc biệt dành cho bạn",
                recipient: "<EMAIL>",
                sender: "<EMAIL>",
                sentDate: "2024-01-21 14:15:00",
                status: "Đã gửi",
                template: "Template Khuyến mãi",
                content: "<h2>Ưu đãi đặc biệt!</h2><p>Giảm giá 50% cho tất cả sản phẩm trong tháng này.</p>"
            },
            email3: {
                subject: "Chào mừng bạn đến với hệ thống",
                recipient: "<EMAIL>",
                sender: "<EMAIL>",
                sentDate: "2024-01-22 09:45:00",
                status: "Thất bại",
                template: "Template Chào mừng",
                content: "<h2>Chào mừng!</h2><p>Cảm ơn bạn đã tham gia hệ thống của chúng tôi.</p>"
            }
        };
        let ipPools = { pool1: { name: "Pool 1", ips: [{ address: "***********" }] }, pool2: { name: "Pool 2", ips: [{ address: "***********" }] } };
        let workers = { worker1: { name: "Worker 1", speed: 100 }, worker2: { name: "Worker 2", speed: 150 } };
        let domains = { domain1: { name: "example.com", status: "Đã xác thực", verificationCode: "" }, domain2: { name: "test.com", status: "Chưa xác thực", verificationCode: "" } };
        let currentDomainId = null;
        let currentTemplateId = null;
        let currentUserId = null;
        let currentSentEmailId = null;

        const itemsPerPage = 5;

        function renderPagination(listId, totalItems, currentPage, renderFunction) {
            const pagination = document.getElementById(`${listId}-pagination`);
            pagination.innerHTML = '';
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            if (totalPages <= 1) return;
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="render${listId.charAt(0).toUpperCase() + listId.slice(1)}(${currentPage - 1});return false;">Trước</a>`;
            pagination.appendChild(prevLi);
            for (let i = 1; i <= totalPages; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="render${listId.charAt(0).toUpperCase() + listId.slice(1)}(${i});return false;">${i}</a>`;
                pagination.appendChild(li);
            }
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="render${listId.charAt(0).toUpperCase() + listId.slice(1)}(${currentPage + 1});return false;">Sau</a>`;
            pagination.appendChild(nextLi);
        }

        function renderUsers(page) {
            const tbody = document.getElementById('user-list');
            tbody.innerHTML = '';
            const searchTerm = document.getElementById('searchUser').value.toLowerCase();
            let filteredUsers = Object.entries(users).filter(([id, user]) => 
                user.fullName.toLowerCase().includes(searchTerm) || user.email.toLowerCase().includes(searchTerm));
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            filteredUsers.slice(start, end).forEach(([id, user]) => {
                const row = document.createElement('tr');
                row.setAttribute('data-id', id);
                row.innerHTML = `<td>${user.fullName}</td><td>${user.email}</td><td>${user.role}</td><td>${user.status}</td><td><button class="btn btn-info btn-sm" onclick="editUser('${id}')">Xem</button></td>`;
                tbody.appendChild(row);
            });
            renderPagination('user', filteredUsers.length, page, renderUsers);
        }

        function renderTemplates(page) {
            const tbody = document.getElementById('template-list');
            tbody.innerHTML = '';
            const searchTerm = document.getElementById('searchTemplate').value.toLowerCase();
            let filteredTemplates = Object.entries(templates).filter(([id, template]) =>
                template.name.toLowerCase().includes(searchTerm));
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            filteredTemplates.slice(start, end).forEach(([id, template]) => {
                const row = document.createElement('tr');
                row.setAttribute('data-id', id);
                row.innerHTML = `<td>${template.name}</td><td>${template.status}</td><td>${template.createdDate}</td><td>
                    <button class="btn btn-warning btn-sm" onclick="editTemplate('${id}')">Edit</button>
                    <button class="btn btn-info btn-sm" onclick="openSendEmailModal('${id}')">Gửi thư</button>
                    <button class="btn btn-${template.status === 'Active' ? 'danger' : 'success'} btn-sm"
                        onclick="toggleTemplateStatus('${id}')">
                        ${template.status === 'Active' ? 'Deactivate' : 'Activate'}
                    </button>
                </td>`;
                tbody.appendChild(row);
            });
            renderPagination('template', filteredTemplates.length, page, renderTemplates);
        }

        function renderSentEmails(page) {
            const tbody = document.getElementById('sent-emails-list');
            tbody.innerHTML = '';
            const searchTerm = document.getElementById('searchSentEmails').value.toLowerCase();
            let filteredEmails = Object.entries(sentEmails).filter(([id, email]) =>
                email.subject.toLowerCase().includes(searchTerm) ||
                email.recipient.toLowerCase().includes(searchTerm));
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            filteredEmails.slice(start, end).forEach(([id, email]) => {
                const row = document.createElement('tr');
                row.setAttribute('data-id', id);
                const statusClass = email.status === 'Đã gửi' ? 'success' : email.status === 'Thất bại' ? 'danger' : 'warning';
                row.innerHTML = `
                    <td>${email.subject}</td>
                    <td>${email.recipient}</td>
                    <td>${email.sentDate}</td>
                    <td><span class="badge bg-${statusClass}">${email.status}</span></td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewSentEmail('${id}')">Xem</button>
                        <button class="btn btn-warning btn-sm" onclick="editAndResendEmail('${id}')">Chỉnh sửa & Gửi lại</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
            renderPagination('sent-emails', filteredEmails.length, page, renderSentEmails);
        }

        function renderIpPools(page) {
            const tbody = document.getElementById('ip-pool-list');
            tbody.innerHTML = '';
            const searchTerm = document.getElementById('searchIpPool').value.toLowerCase();
            let filteredIpPools = Object.entries(ipPools).filter(([id, pool]) => 
                pool.name.toLowerCase().includes(searchTerm));
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            filteredIpPools.slice(start, end).forEach(([id, pool]) => {
                const row = document.createElement('tr');
                row.setAttribute('data-id', id);
                row.innerHTML = `<td>${pool.name}</td><td>${pool.ips.length}</td><td><button class="btn btn-info btn-sm">Xem</button></td>`;
                tbody.appendChild(row);
            });
            renderPagination('ip-pool', filteredIpPools.length, page, renderIpPools);
        }

        function renderWorkers(page) {
            const tbody = document.getElementById('worker-list');
            tbody.innerHTML = '';
            const searchTerm = document.getElementById('searchWorker').value.toLowerCase();
            let filteredWorkers = Object.entries(workers).filter(([id, worker]) => 
                worker.name.toLowerCase().includes(searchTerm));
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            filteredWorkers.slice(start, end).forEach(([id, worker]) => {
                const row = document.createElement('tr');
                row.setAttribute('data-id', id);
                row.innerHTML = `<td>${worker.name}</td><td>${worker.speed}</td><td><button class="btn btn-warning btn-sm">Chỉnh sửa</button></td>`;
                tbody.appendChild(row);
            });
            renderPagination('worker', filteredWorkers.length, page, renderWorkers);
        }

        function renderDomains(page) {
            const tbody = document.getElementById('domain-list');
            tbody.innerHTML = '';
            const searchTerm = document.getElementById('searchDomain').value.toLowerCase();
            let filteredDomains = Object.entries(domains).filter(([id, domain]) => 
                domain.name.toLowerCase().includes(searchTerm));
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            filteredDomains.slice(start, end).forEach(([id, domain]) => {
                const row = document.createElement('tr');
                row.setAttribute('data-id', id);
                const verificationCode = domain.verificationCode || 'xai-' + Math.random().toString(36).substr(2, 8);
                if (!domain.verificationCode) {
                    domain.verificationCode = verificationCode;
                }
                row.innerHTML = `
                    <td>${domain.name}</td>
                    <td>${domain.status}</td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="viewDomainDetail('${id}')">Xem chi tiết</button>
                        ${domain.status === 'Chưa xác thực' ? `
                            <button class="btn btn-success btn-sm" onclick="toggleVerificationForm('${id}')">Xác thực</button>
                            <button class="btn btn-primary btn-sm" onclick="verifyDomain('${id}')">Hướng dẫn xác thực</button>
                        ` : ''}
                    </td>
                `;
                if (domain.status === 'Chưa xác thực') {
                    const verificationRow = document.createElement('tr');
                    verificationRow.className = `verification-input`;
                    verificationRow.id = `verification-form-${id}`;
                    verificationRow.innerHTML = `
                        <td colspan="3">
                            <div class="mb-2"><label class="form-label">Loại bản ghi</label><input type="text" class="form-control" id="dnsRecordType-${id}" placeholder="Nhập loại bản ghi (ví dụ: TXT)"></div>
                            <div class="mb-2"><label class="form-label">Tên</label><input type="text" class="form-control" id="dnsRecordName-${id}" placeholder="(ví dụ: _acme-challenge hoặc @)"></div>
                            <div class="mb-2"><label class="form-label">Giá trị</label><input type="text" class="form-control" id="dnsRecordValue-${id}" placeholder="Nhập giá trị (kỳ vọng: ${verificationCode})"></div>
                            <div class="mb-2"><label class="form-label">TTL</label><input type="number" class="form-control" id="dnsRecordTTL-${id}" placeholder="Nhập TTL (ví dụ: 3600)"></div>
                            <button class="btn btn-primary" onclick="verifyDomainDirectly('${id}')">Xác thực ngay</button>
                            <button class="btn btn-secondary" onclick="toggleVerificationForm('${id}')">Hủy</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                    tbody.appendChild(verificationRow);
                } else {
                    tbody.appendChild(row);
                }
            });
            renderPagination('domain', filteredDomains.length, page, renderDomains);
        }

        function searchUsers() { renderUsers(1); }
        function searchTemplates() { renderTemplates(1); }
        function searchSentEmails() { renderSentEmails(1); }
        function searchIpPools() { renderIpPools(1); }
        function searchWorkers() { renderWorkers(1); }
        function searchDomains() { renderDomains(1); }

        function toggleVerificationForm(domainId) { const form = document.getElementById(`verification-form-${domainId}`); form.classList.toggle('active'); }

        function editUser(userId) {
            currentUserId = userId;
            const user = users[userId];
            document.getElementById('editUserId').textContent = user.fullName;
            document.getElementById('editUserFullName').value = user.fullName;
            document.getElementById('editUserEmail').value = user.email;
            document.getElementById('editUserRole').value = user.role;
            document.getElementById('editUserStatus').value = user.status;
            switchSection('edit-user');
        }

        function saveUserChanges() {
            if (!currentUserId) return;
            const fullName = document.getElementById('editUserFullName').value;
            const email = document.getElementById('editUserEmail').value;
            const role = document.getElementById('editUserRole').value;
            const status = document.getElementById('editUserStatus').value;

            if (fullName && email) {
                users[currentUserId].fullName = fullName;
                users[currentUserId].email = email;
                users[currentUserId].role = role;
                users[currentUserId].status = status;
                alert('Thông tin tài khoản đã được cập nhật!');
                switchSection('user-list');
                renderUsers(1);
            } else {
                alert('Vui lòng nhập đầy đủ thông tin!');
            }
        }

        function viewSentEmail(emailId) {
            currentSentEmailId = emailId;
            const email = sentEmails[emailId];
            document.getElementById('sentEmailSubject').textContent = email.subject;
            document.getElementById('sentEmailSender').textContent = email.sender;
            document.getElementById('sentEmailRecipient').textContent = email.recipient;
            document.getElementById('sentEmailDate').textContent = email.sentDate;
            document.getElementById('sentEmailStatus').innerHTML = `<span class="badge bg-${email.status === 'Đã gửi' ? 'success' : email.status === 'Thất bại' ? 'danger' : 'warning'}">${email.status}</span>`;
            document.getElementById('sentEmailTemplate').textContent = email.template;
            document.getElementById('sentEmailContent').innerHTML = email.content;
            switchSection('view-sent-email');
        }

        function editSentEmail() {
            if (!currentSentEmailId) return;
            const email = sentEmails[currentSentEmailId];

            // Find template ID by name
            let templateId = '';
            Object.entries(templates).forEach(([id, template]) => {
                if (template.name === email.template) {
                    templateId = id;
                }
            });

            // Open send email modal with pre-filled data
            openSendEmailModal(templateId);
            document.getElementById('sendEmailRecipient').value = email.recipient;
            document.getElementById('sendEmailSubject').value = email.subject;
            document.getElementById('sendEmailContent').value = email.content;
        }

        function editAndResendEmail(emailId) {
            currentSentEmailId = emailId;
            editSentEmail();
        }
        function createUser() { const fullName = document.getElementById('createFullName').value; const email = document.getElementById('createEmail').value; const password = document.getElementById('createPassword').value; if (fullName && email && password) { const newId = 'user' + (Object.keys(users).length + 1); users[newId] = { fullName, email, role: "User", status: "Active" }; renderUsers(1); alert('Tài khoản đã được tạo!'); document.getElementById('createFullName').value = ''; document.getElementById('createEmail').value = ''; document.getElementById('createPassword').value = ''; bootstrap.Modal.getInstance(document.getElementById('createUserModal')).hide(); } else alert('Vui lòng nhập đầy đủ thông tin!'); }
        function createIpPool() { const name = document.getElementById('ipPoolNameInput').value; if (name) { const newId = 'pool' + (Object.keys(ipPools).length + 1); ipPools[newId] = { name, ips: [] }; renderIpPools(1); alert('IP Pool đã được tạo!'); document.getElementById('ipPoolNameInput').value = ''; bootstrap.Modal.getInstance(document.getElementById('createIpPoolModal')).hide(); } else alert('Vui lòng nhập tên IP Pool!'); }
        function addIpToPool() { const ipAddress = document.getElementById('ipAddressInput').value; if (ipAddress && currentIpPoolId) { const pool = ipPools[currentIpPoolId]; pool.ips.push({ address: ipAddress }); alert('IP đã được thêm!'); document.getElementById('ipAddressInput').value = ''; bootstrap.Modal.getInstance(document.getElementById('addIpModal')).hide(); } else alert('Vui lòng nhập địa chỉ IP!'); }
        function createWorker() { const name = document.getElementById('workerNameInput').value; const speed = document.getElementById('workerSpeedInput').value; if (name && speed) { const newId = 'worker' + (Object.keys(workers).length + 1); workers[newId] = { name, speed: parseInt(speed) }; renderWorkers(1); alert('Worker đã được tạo!'); document.getElementById('workerNameInput').value = ''; document.getElementById('workerSpeedInput').value = ''; bootstrap.Modal.getInstance(document.getElementById('createWorkerModal')).hide(); } else alert('Vui lòng nhập đầy đủ thông tin!'); }
        function createDomain() { const domainName = document.getElementById('domainNameInput').value; if (domainName) { const newId = 'domain' + (Object.keys(domains).length + 1); domains[newId] = { name: domainName, status: "Chưa xác thực", verificationCode: "" }; renderDomains(1); alert('Tên miền đã được tạo!'); document.getElementById('domainNameInput').value = ''; bootstrap.Modal.getInstance(document.getElementById('createDomainModal')).hide(); } else alert('Vui lòng nhập tên miền!'); }
        function saveTemplate() {
            const name = document.getElementById('templateName').value;
            const subject = document.getElementById('templateSubject').value;
            const content = document.getElementById('templateContent').value;
            if (name && subject && content) {
                const newId = 'template' + (Object.keys(templates).length + 1);
                const currentDate = new Date().toISOString().split('T')[0];
                templates[newId] = {
                    name,
                    subject,
                    content,
                    status: "Bản nháp",
                    createdDate: currentDate
                };
                renderTemplates(1);
                alert('Template đã được lưu!');
                document.getElementById('templateName').value = '';
                document.getElementById('templateSubject').value = '';
                document.getElementById('templateContent').value = '';
                bootstrap.Modal.getInstance(document.getElementById('createTemplateModal')).hide();
            } else alert('Vui lòng nhập đầy đủ thông tin!');
        }

        function editTemplate(templateId) {
            currentTemplateId = templateId;
            const template = templates[templateId];
            document.getElementById('editTemplateName').value = template.name;
            document.getElementById('editTemplateSubject').value = template.subject;
            document.getElementById('editTemplateContent').value = template.content;
            bootstrap.Modal.getInstance(document.getElementById('editTemplateModal')).show();
        }

        function updateTemplate() {
            if (!currentTemplateId) return;
            const name = document.getElementById('editTemplateName').value;
            const subject = document.getElementById('editTemplateSubject').value;
            const content = document.getElementById('editTemplateContent').value;
            if (name && subject && content) {
                templates[currentTemplateId].name = name;
                templates[currentTemplateId].subject = subject;
                templates[currentTemplateId].content = content;
                renderTemplates(1);
                alert('Template đã được cập nhật!');
                bootstrap.Modal.getInstance(document.getElementById('editTemplateModal')).hide();
            } else alert('Vui lòng nhập đầy đủ thông tin!');
        }

        function openSendEmailModal(templateId) {
            currentTemplateId = templateId;
            // Populate template dropdown
            const templateSelect = document.getElementById('sendEmailTemplate');
            templateSelect.innerHTML = '<option value="">-- Chọn template --</option>';
            Object.entries(templates).forEach(([id, template]) => {
                if (template.status === 'Active') {
                    const option = document.createElement('option');
                    option.value = id;
                    option.textContent = template.name;
                    if (id === templateId) option.selected = true;
                    templateSelect.appendChild(option);
                }
            });

            if (templateId) {
                loadTemplateForSending();
            }
            bootstrap.Modal.getInstance(document.getElementById('sendEmailModal')).show();
        }

        function loadTemplateForSending() {
            const templateId = document.getElementById('sendEmailTemplate').value;
            if (templateId && templates[templateId]) {
                const template = templates[templateId];
                document.getElementById('sendEmailSubject').value = template.subject;
                document.getElementById('sendEmailContent').value = template.content;
            }
        }

        function sendEmail() {
            const templateId = document.getElementById('sendEmailTemplate').value;
            const recipient = document.getElementById('sendEmailRecipient').value;
            const subject = document.getElementById('sendEmailSubject').value;
            const content = document.getElementById('sendEmailContent').value;

            if (recipient && subject && content) {
                const newId = 'email' + (Object.keys(sentEmails).length + 1);
                const currentDateTime = new Date().toLocaleString('vi-VN');
                sentEmails[newId] = {
                    subject: subject,
                    recipient: recipient,
                    sender: "<EMAIL>",
                    sentDate: currentDateTime,
                    status: "Đã gửi",
                    template: templateId ? templates[templateId].name : "Tùy chỉnh",
                    content: content
                };

                alert('Email đã được gửi thành công!');
                document.getElementById('sendEmailRecipient').value = '';
                document.getElementById('sendEmailSubject').value = '';
                document.getElementById('sendEmailContent').value = '';
                bootstrap.Modal.getInstance(document.getElementById('sendEmailModal')).hide();
            } else {
                alert('Vui lòng nhập đầy đủ thông tin!');
            }
        }

        function viewDomainDetail(domainId) { currentDomainId = domainId; const domain = domains[domainId]; document.getElementById('detailDomainName').textContent = domain.name; document.getElementById('detailDomainStatus').textContent = domain.status; document.getElementById('detailVerificationCode').textContent = domain.verificationCode || 'Không có mã'; switchSection('domain-detail'); }
        function verifyDomain(domainId) { currentDomainId = domainId; const domain = domains[domainId]; const newVerificationCode = 'xai-' + Math.random().toString(36).substr(2, 8); domain.verificationCode = newVerificationCode; document.getElementById('verifyDomainName').textContent = domain.name; document.getElementById('verificationCode').textContent = newVerificationCode; document.getElementById('dnsRecordType').value = ''; document.getElementById('dnsRecordName').value = ''; document.getElementById('dnsRecordValue').value = ''; document.getElementById('dnsRecordTTL').value = ''; document.getElementById('verificationFile').value = ''; switchSection('domain-verification'); }
        function verifyDomainDirectly(domainId) { 
            currentDomainId = domainId; 
            const domain = domains[domainId]; 
            const expectedType = "TXT"; 
            const expectedName = "_acme-challenge"; 
            const expectedValue = domain.verificationCode; 
            const expectedTTL = 3600; 
            const inputType = document.getElementById(`dnsRecordType-${domainId}`).value.trim(); 
            const inputName = document.getElementById(`dnsRecordName-${domainId}`).value.trim(); 
            const inputValue = document.getElementById(`dnsRecordValue-${domainId}`).value.trim(); 
            const inputTTL = parseInt(document.getElementById(`dnsRecordTTL-${domainId}`).value.trim()); 
            if (!inputType || !inputName || !inputValue || isNaN(inputTTL)) { 
                alert('Vui lòng nhập đầy đủ thông tin bản ghi DNS!'); 
                return; 
            } 
            if (inputType.toUpperCase() === expectedType && inputName === expectedName && inputValue === expectedValue && inputTTL === expectedTTL) { 
                domain.status = "Đã xác thực"; 
                domain.verificationCode = ""; 
                alert('Tên miền đã được xác thực thành công!'); 
                renderDomains(1); 
            } else { 
                let errorMessage = 'Xác thực thất bại! Vui lòng kiểm tra lại thông tin:\n'; 
                if (inputType.toUpperCase() !== expectedType) errorMessage += `- Loại bản ghi: Nhập ${inputType}, kỳ vọng ${expectedType}\n`; 
                if (inputName !== expectedName) errorMessage += `- Tên: Nhập ${inputName}, kỳ vọng ${expectedName}\n`; 
                if (inputValue !== expectedValue) errorMessage += `- Giá trị: Nhập ${inputValue}, kỳ vọng ${expectedValue}\n`; 
                if (inputTTL !== expectedTTL) errorMessage += `- TTL: Nhập ${inputTTL}, kỳ vọng ${expectedTTL}\n`; 
                alert(errorMessage); 
            } 
        }
        function verifyDomainCheck() { if (!currentDomainId) { alert('Không tìm thấy tên miền để xác thực!'); return; } const domain = domains[currentDomainId]; const expectedType = "TXT"; const expectedName = "_acme-challenge"; const expectedValue = domain.verificationCode; const expectedTTL = 3600; const inputType = document.getElementById('dnsRecordType').value.trim(); const inputName = document.getElementById('dnsRecordName').value.trim(); const inputValue = document.getElementById('dnsRecordValue').value.trim(); const inputTTL = parseInt(document.getElementById('dnsRecordTTL').value.trim()); if (!inputType || !inputName || !inputValue || isNaN(inputTTL)) { alert('Vui lòng nhập đầy đủ thông tin bản ghi DNS!'); return; } if (inputType.toUpperCase() === expectedType && inputName === expectedName && inputValue === expectedValue && inputTTL === expectedTTL) { domain.status = "Đã xác thực"; domain.verificationCode = ""; alert('Tên miền đã được xác thực thành công!'); switchSection('domain-management'); renderDomains(1); } else { let errorMessage = 'Xác thực thất bại! Vui lòng kiểm tra lại thông tin:\n'; if (inputType.toUpperCase() !== expectedType) errorMessage += `- Loại bản ghi: Nhập ${inputType}, kỳ vọng ${expectedType}\n`; if (inputName !== expectedName) errorMessage += `- Tên: Nhập ${inputName}, kỳ vọng ${expectedName}\n`; if (inputValue !== expectedValue) errorMessage += `- Giá trị: Nhập ${inputValue}, kỳ vọng ${expectedValue}\n`; if (inputTTL !== expectedTTL) errorMessage += `- TTL: Nhập ${inputTTL}, kỳ vọng ${expectedTTL}\n`; alert(errorMessage); } }
        function verifyDomainWithFile() { if (!currentDomainId) { alert('Không tìm thấy tên miền để xác thực!'); return; } const domain = domains[currentDomainId]; const fileInput = document.getElementById('verificationFile'); const file = fileInput.files[0]; if (!file) { alert('Vui lòng chọn một file để tải lên!'); return; } if (file.type !== 'text/plain') { alert('Vui lòng tải lên file định dạng .txt!'); return; } const reader = new FileReader(); reader.onload = function(e) { const fileContent = e.target.result.trim(); const expectedValue = domain.verificationCode; if (fileContent === expectedValue) { domain.status = "Đã xác thực"; domain.verificationCode = ""; alert('Tên miền đã được xác thực thành công bằng file!'); switchSection('domain-management'); renderDomains(1); } else { alert(`Xác thực thất bại! Nội dung file không khớp với mã xác thực. Nội dung file: ${fileContent}, kỳ vọng: ${expectedValue}`); } }; reader.onerror = function() { alert('Có lỗi khi đọc file. Vui lòng thử lại!'); }; reader.readAsText(file); }

        function toggleTemplateStatus(templateId) {
            const template = templates[templateId];
            if (template.status === 'Active') {
                template.status = 'Deactive';
            } else {
                template.status = 'Active';
            }
            renderTemplates(1);
        }

        window.onload = () => {
            renderUsers(1);
            renderTemplates(1);
            renderSentEmails(1);
            renderIpPools(1);
            renderWorkers(1);
            renderDomains(1);
        };
    </script>
</body>
</html>
