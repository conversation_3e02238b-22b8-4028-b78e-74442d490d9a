<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Marketing Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }
        .dashboard-container {
            min-height: 100vh;
            display: flex;
        }
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .main-content {
            flex: 1;
            padding: 2rem;
            background-color: #f8f9fa;
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .content-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .hidden {
            display: none !important;
        }
        .error-message {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        .user-info {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .email-toolbar {
            display: flex;
            gap: 0.25rem;
            flex-wrap: wrap;
        }
        .search-container {
            max-width: 300px;
        }
        .sub-menu {
            margin-left: 1rem;
            margin-top: 0.5rem;
        }
        .sub-menu .nav-link {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="login-screen" class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <i class="fas fa-envelope-open-text fa-3x text-primary mb-3"></i>
                <h3>Email Marketing</h3>
                <p class="text-muted">Đăng nhập vào tài khoản của bạn</p>
            </div>
            <form onsubmit="event.preventDefault(); login();">
                <div class="mb-3">
                    <label for="loginEmail" class="form-label">Email</label>
                    <input type="email" class="form-control" id="loginEmail" value="<EMAIL>" required>
                    <div id="emailError" class="error-message hidden">Email không hợp lệ</div>
                </div>
                <div class="mb-3">
                    <label for="loginPassword" class="form-label">Mật khẩu</label>
                    <input type="password" class="form-control" id="loginPassword" value="password123" required>
                    <div id="passwordError" class="error-message hidden">Mật khẩu không đúng</div>
                </div>
                <div id="loginError" class="error-message hidden mb-3">Tài khoản không tồn tại</div>
                <button type="submit" class="btn btn-primary w-100 mb-3">Đăng nhập</button>
                <div class="text-center">
                    <a href="#" class="text-decoration-none" onclick="showForgotPassword()">Quên mật khẩu?</a>
                </div>
            </form>
        </div>
    </div>

    <!-- User Dashboard -->
    <div id="user-dashboard" class="dashboard-container hidden">
        <div class="sidebar">
            <div class="user-info">
                <div class="d-flex align-items-center">
                    <i class="fas fa-user-circle fa-2x me-2"></i>
                    <div>
                        <div class="fw-bold" id="userFullName">Nguyễn Văn A</div>
                        <small id="userEmail"><EMAIL></small>
                    </div>
                </div>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" data-section="email-management">Quản lý thư điện tử</a>
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item"><a class="nav-link active" data-section="compose-email">Tạo mới thư</a></li>
                        <li class="nav-item"><a class="nav-link" data-section="email-list">Danh sách thư</a></li>
                    </ul>
                </li>
                <li class="nav-item"><a class="nav-link text-danger" onclick="logout()">Đăng xuất</a></li>
            </ul>
        </div>
        <div class="main-content">
            <!-- Compose Email Section -->
            <div class="content-section" id="compose-email-section">
                <h5>Soạn thư mới</h5>
                <div class="mb-3">
                    <label for="emailTo" class="form-label">Người nhận</label>
                    <div class="input-group">
                        <input type="email" class="form-control" id="emailTo" placeholder="Nhập email người nhận">
                        <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#importContactsModal">
                            <i class="fas fa-users"></i> Import danh sách
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="emailSubject" class="form-label">Tiêu đề</label>
                    <input type="text" class="form-control" id="emailSubject" placeholder="Nhập tiêu đề email">
                </div>
                <div class="mb-3">
                    <label for="emailContent" class="form-label">Nội dung</label>
                    <textarea class="form-control" id="emailContent" rows="10" placeholder="Nhập nội dung email"></textarea>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="scheduleEmail">
                    <label class="form-check-label" for="scheduleEmail">Hẹn giờ gửi</label>
                </div>
                <div class="mb-3 hidden" id="scheduleTimeContainer">
                    <label for="scheduleTime" class="form-label">Thời gian gửi</label>
                    <input type="datetime-local" class="form-control" id="scheduleTime">
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" onclick="sendEmail()">Gửi</button>
                    <button class="btn btn-secondary" onclick="saveAsDraft()">Lưu bản nháp</button>
                </div>
            </div>

            <!-- Email List Section -->
            <div class="content-section d-none" id="email-list-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Danh sách thư</h5>
                    <div class="d-flex gap-2">
                        <select class="form-control" id="statusFilter" onchange="filterEmails()" style="width: 150px;">
                            <option value="all">Tất cả</option>
                            <option value="draft">Bản nháp</option>
                            <option value="sent">Đã gửi</option>
                            <option value="failed">Thất bại</option>
                        </select>
                        <input type="text" class="form-control" id="searchEmails" placeholder="Tìm kiếm..." onkeyup="filterEmails()">
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Người nhận</th>
                            <th>Tiêu đề</th>
                            <th>Thời gian</th>
                            <th>Trạng thái</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody id="email-list">
                        <!-- Emails will be populated here -->
                    </tbody>
                </table>
            </div>

            <!-- User Profile Section -->
            <div class="content-section d-none" id="user-profile-section">
                <h5>Thông tin tài khoản</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="profileFullName" class="form-label">Họ tên</label>
                        <input type="text" class="form-control" id="profileFullName" value="Nguyễn Văn A">
                    </div>
                    <div class="col-md-6">
                        <label for="profileEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="profileEmail" value="<EMAIL>" readonly>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="profilePhone" class="form-label">Số điện thoại</label>
                        <input type="tel" class="form-control" id="profilePhone" value="0123456789">
                    </div>
                    <div class="col-md-6">
                        <label for="profileBirthday" class="form-label">Ngày sinh</label>
                        <input type="date" class="form-control" id="profileBirthday" value="1990-01-01">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="profileAddress" class="form-label">Địa chỉ</label>
                    <textarea class="form-control" id="profileAddress" rows="3">123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh</textarea>
                </div>
                
                <!-- Phần đổi mật khẩu -->
                <div class="mt-4 mb-3">
                    <div class="row align-items-center mb-3">
                        <div class="col-4">
                            <h5 class="mb-0">Đổi mật khẩu</h5>
                        </div>
                        <div class="col-4 text-center">
                            <button class="btn btn-warning" onclick="changePassword()">Đổi mật khẩu</button>
                        </div>
                        <div class="col-4"></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="currentPassword" class="form-label">Mật khẩu hiện tại</label>
                            <input type="password" class="form-control" id="currentPassword" placeholder="Nhập mật khẩu hiện tại">
                            <div id="currentPasswordError" class="error-message hidden">Mật khẩu hiện tại không đúng</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="newPassword" class="form-label">Mật khẩu mới</label>
                            <input type="password" class="form-control" id="newPassword" placeholder="Nhập mật khẩu mới">
                            <div id="newPasswordError" class="error-message hidden">Mật khẩu mới phải có ít nhất 6 ký tự</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="confirmPassword" class="form-label">Xác nhận mật khẩu mới</label>
                            <input type="password" class="form-control" id="confirmPassword" placeholder="Nhập lại mật khẩu mới">
                            <div id="confirmPasswordError" class="error-message hidden">Mật khẩu xác nhận không khớp</div>
                        </div>
                    </div>
                    <div id="passwordChangeSuccess" class="alert alert-success mt-2 hidden">Mật khẩu đã được thay đổi thành công!</div>
                </div>
                
                <button class="btn btn-primary mt-3" onclick="saveProfile()">Lưu thay đổi</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample data
        let currentUser = null;
        const users = {
            "<EMAIL>": {
                password: "password123",
                fullName: "Nguyễn Văn A",
                phone: "0123456789",
                birthday: "1990-01-01",
                address: "123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh"
            }
        };
        
        let sentEmails = [
            {
                id: 1,
                to: "<EMAIL>",
                subject: "Chào mừng bạn đến với dịch vụ của chúng tôi",
                content: "<h2>Chào mừng!</h2><p>Cảm ơn bạn đã đăng ký sử dụng dịch vụ email marketing của chúng tôi. Chúng tôi rất vui mừng được phục vụ bạn!</p><p>Nếu có bất kỳ câu hỏi nào, vui lòng liên hệ với chúng tôi.</p>",
                sentTime: "15/01/2024 10:30:00",
                status: "Đã gửi"
            },
            {
                id: 2,
                to: "<EMAIL>",
                subject: "Ưu đãi đặc biệt - Giảm giá 50%",
                content: "<h2>Ưu đãi đặc biệt!</h2><p>Chúng tôi có chương trình khuyến mãi đặc biệt dành riêng cho bạn:</p><ul><li>Giảm giá 50% cho tất cả gói dịch vụ</li><li>Miễn phí setup và hỗ trợ</li><li>Tặng kèm 1000 email miễn phí</li></ul><p>Ưu đãi có hiệu lực đến hết tháng này!</p>",
                sentTime: "20/01/2024 14:15:00",
                status: "Đã gửi"
            },
            {
                id: 3,
                to: "<EMAIL>",
                subject: "Thông báo bảo trì hệ thống",
                content: "<h2>Thông báo bảo trì</h2><p>Kính gửi quý khách hàng,</p><p>Chúng tôi xin thông báo hệ thống sẽ được bảo trì vào:</p><p><strong>Thời gian:</strong> 02:00 - 04:00 sáng ngày mai</p><p>Trong thời gian này, dịch vụ có thể bị gián đoạn. Chúng tôi xin lỗi vì sự bất tiện này.</p>",
                sentTime: "22/01/2024 09:45:00",
                status: "Thất bại"
            }
        ];
        
        let draftEmails = [
            {
                id: 1,
                to: "<EMAIL>",
                subject: "Thông báo sự kiện mới",
                content: "Kính gửi quý khách hàng,\n\nChúng tôi xin thông báo về sự kiện đặc biệt sắp tới...",
                savedTime: "25/01/2024 16:20:00"
            },
            {
                id: 2,
                to: "",
                subject: "Newsletter tháng 2",
                content: "Chào mừng đến với newsletter tháng 2...",
                savedTime: "26/01/2024 09:15:00"
            }
        ];

        // Login function
        function login() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            // Reset error messages
            document.getElementById('emailError').classList.add('hidden');
            document.getElementById('passwordError').classList.add('hidden');
            document.getElementById('loginError').classList.add('hidden');

            // Validate email format
            if (!email || !email.includes('@')) {
                document.getElementById('emailError').classList.remove('hidden');
                return;
            }

            // Check if user exists
            if (users[email]) {
                // Check password
                if (users[email].password === password) {
                    currentUser = {
                        email: email,
                        ...users[email]
                    };

                    // Update user info in dashboard
                    document.getElementById('userFullName').textContent = currentUser.fullName;
                    document.getElementById('userEmail').textContent = currentUser.email;

                    // Show dashboard, hide login
                    document.getElementById('login-screen').classList.add('hidden');
                    document.getElementById('user-dashboard').classList.remove('hidden');

                    // Load initial data
                    filterEmails();
                } else {
                    document.getElementById('passwordError').classList.remove('hidden');
                }
            } else {
                document.getElementById('loginError').classList.remove('hidden');
            }
        }

        // Logout function
        function logout() {
            currentUser = null;
            document.getElementById('login-screen').classList.remove('hidden');
            document.getElementById('user-dashboard').classList.add('hidden');
            document.getElementById('loginEmail').value = '';
            document.getElementById('loginPassword').value = '';
        }

        // Switch between sections
        function switchSection(section) {
            // Hide all content sections
            document.querySelectorAll('.main-content .content-section').forEach(sec => sec.classList.add('d-none'));

            // Remove active class from all nav links
            document.querySelectorAll('.sidebar .nav-link').forEach(link => link.classList.remove('active'));

            // Show selected section
            document.getElementById(`${section}-section`).classList.remove('d-none');

            // Add active class to selected nav link
            const navLink = document.querySelector(`.nav-link[data-section="${section}"]`);
            if (navLink) navLink.classList.add('active');

            // Load data based on section
            if (section === 'email-list') {
                filterEmails();
            }
        }

        // Filter emails by status and search term
        function filterEmails() {
            const statusFilter = document.getElementById('statusFilter').value;
            const searchTerm = document.getElementById('searchEmails').value.toLowerCase();
            const tbody = document.getElementById('email-list');
            tbody.innerHTML = '';

            // Combine all emails with their status
            let allEmails = [];

            // Add sent emails
            sentEmails.forEach(email => {
                allEmails.push({
                    ...email,
                    time: email.sentTime,
                    type: 'sent'
                });
            });

            // Add draft emails
            draftEmails.forEach(email => {
                allEmails.push({
                    ...email,
                    status: 'Bản nháp',
                    time: email.savedTime,
                    type: 'draft'
                });
            });

            // Filter by status
            if (statusFilter !== 'all') {
                if (statusFilter === 'draft') {
                    allEmails = allEmails.filter(email => email.status === 'Bản nháp');
                } else if (statusFilter === 'sent') {
                    allEmails = allEmails.filter(email => email.status === 'Đã gửi');
                } else if (statusFilter === 'failed') {
                    allEmails = allEmails.filter(email => email.status === 'Thất bại');
                }
            }

            // Filter by search term
            if (searchTerm) {
                allEmails = allEmails.filter(email =>
                    email.to.toLowerCase().includes(searchTerm) ||
                    email.subject.toLowerCase().includes(searchTerm) ||
                    (email.content && email.content.toLowerCase().includes(searchTerm))
                );
            }

            // Sort by time (newest first)
            allEmails.sort((a, b) => new Date(b.time) - new Date(a.time));

            if (allEmails.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" class="text-center">Không tìm thấy kết quả phù hợp</td>';
                tbody.appendChild(row);
            } else {
                allEmails.forEach(email => {
                    const row = document.createElement('tr');
                    const statusClass = email.status === 'Đã gửi' ? 'success' :
                                       email.status === 'Thất bại' ? 'danger' : 'warning';

                    let actions = '';
                    if (email.type === 'draft') {
                        actions = `
                            <button class="btn btn-sm btn-warning" onclick="editEmail(${email.id}, 'draft')">Chỉnh sửa</button>
                            <button class="btn btn-sm btn-success" onclick="sendDraftEmail(${email.id})">Gửi</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteEmail(${email.id}, 'draft')">Xóa</button>
                        `;
                    } else {
                        actions = `
                            <button class="btn btn-sm btn-info" onclick="viewEmail(${email.id}, 'sent')">Xem</button>
                            <button class="btn btn-sm btn-warning" onclick="editEmail(${email.id}, 'sent')">Gửi lại</button>
                        `;
                    }

                    row.innerHTML = `
                        <td>${email.to || '(Chưa có người nhận)'}</td>
                        <td>${email.subject}</td>
                        <td>${email.time}</td>
                        <td><span class="badge bg-${statusClass}">${email.status}</span></td>
                        <td>${actions}</td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        // Edit email (for both draft and sent)
        function editEmail(id, type) {
            let email;
            if (type === 'draft') {
                email = draftEmails.find(e => e.id === id);
            } else {
                email = sentEmails.find(e => e.id === id);
            }

            if (!email) return;

            // Fill the compose form with email data
            document.getElementById('emailTo').value = email.to || '';
            document.getElementById('emailSubject').value = email.subject || '';
            document.getElementById('emailContent').value = email.content.replace(/<[^>]*>/g, ''); // Remove HTML tags for editing

            // If editing draft, remove it from drafts
            if (type === 'draft') {
                draftEmails = draftEmails.filter(e => e.id !== id);
            }

            // Switch to compose section
            switchSection('compose-email');

            alert('Email đã được tải vào form soạn thư. Bạn có thể chỉnh sửa và gửi.');
        }

        // Send draft email
        function sendDraftEmail(id) {
            const email = draftEmails.find(e => e.id === id);
            if (!email) return;

            if (!email.to) {
                alert('Vui lòng chỉnh sửa email và thêm người nhận trước khi gửi!');
                editEmail(id, 'draft');
                return;
            }

            // Move from draft to sent
            const now = new Date().toLocaleString('vi-VN');
            const newSentId = sentEmails.length > 0 ? Math.max(...sentEmails.map(e => e.id)) + 1 : 1;

            sentEmails.push({
                id: newSentId,
                to: email.to,
                subject: email.subject,
                content: email.content,
                sentTime: now,
                status: 'Đã gửi'
            });

            // Remove from drafts
            draftEmails = draftEmails.filter(e => e.id !== id);

            alert('Email đã được gửi thành công!');
            filterEmails(); // Refresh the list
        }

        // View email details
        function viewEmail(id, type) {
            let email;
            if (type === 'draft') {
                email = draftEmails.find(e => e.id === id);
            } else {
                email = sentEmails.find(e => e.id === id);
            }

            if (!email) return;

            alert(`Chi tiết email:\n\nNgười nhận: ${email.to || '(Chưa có)'}\nTiêu đề: ${email.subject}\nThời gian: ${email.time || email.sentTime || email.savedTime}\nTrạng thái: ${email.status || (type === 'draft' ? 'Bản nháp' : 'Đã gửi')}\n\nNội dung:\n${email.content.replace(/<[^>]*>/g, '')}`);
        }

        // Delete email
        function deleteEmail(id, type) {
            if (!confirm('Bạn có chắc muốn xóa email này?')) return;

            if (type === 'draft') {
                draftEmails = draftEmails.filter(e => e.id !== id);
            }

            filterEmails(); // Refresh the list
        }

        // Send email function
        function sendEmail() {
            const to = document.getElementById('emailTo').value;
            const subject = document.getElementById('emailSubject').value;
            const content = document.getElementById('emailContent').value;
            const isScheduled = document.getElementById('scheduleEmail').checked;

            if (!to || !subject || !content) {
                alert('Vui lòng nhập đầy đủ thông tin email!');
                return;
            }

            if (isScheduled) {
                const scheduleTime = document.getElementById('scheduleTime').value;
                if (!scheduleTime) {
                    alert('Vui lòng chọn thời gian gửi!');
                    return;
                }
                alert('Chức năng hẹn giờ sẽ được phát triển trong phiên bản tiếp theo!');
                return;
            } else {
                // Add to sent emails
                const now = new Date().toLocaleString('vi-VN');
                const newId = sentEmails.length > 0 ? Math.max(...sentEmails.map(e => e.id)) + 1 : 1;
                sentEmails.push({
                    id: newId,
                    to: to,
                    subject: subject,
                    content: content,
                    sentTime: now,
                    status: 'Đã gửi'
                });

                alert('Email đã được gửi thành công!');
            }

            // Clear the form
            document.getElementById('emailTo').value = '';
            document.getElementById('emailSubject').value = '';
            document.getElementById('emailContent').value = '';
            document.getElementById('scheduleEmail').checked = false;
            document.getElementById('scheduleTimeContainer').classList.add('hidden');
        }

        // Save as draft function
        function saveAsDraft() {
            const to = document.getElementById('emailTo').value;
            const subject = document.getElementById('emailSubject').value;
            const content = document.getElementById('emailContent').value;

            if (!to && !subject && !content) {
                alert('Không có nội dung để lưu!');
                return;
            }

            // Add to drafts
            const now = new Date().toLocaleString('vi-VN');
            const newId = draftEmails.length > 0 ? Math.max(...draftEmails.map(e => e.id)) + 1 : 1;
            draftEmails.push({
                id: newId,
                to: to || '',
                subject: subject || '(Chưa có tiêu đề)',
                content: content,
                savedTime: now
            });

            alert('Email đã được lưu vào bản nháp!');

            // Clear the form
            document.getElementById('emailTo').value = '';
            document.getElementById('emailSubject').value = '';
            document.getElementById('emailContent').value = '';
        }

        // Toggle schedule time container
        document.getElementById('scheduleEmail').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('scheduleTimeContainer').classList.remove('hidden');
            } else {
                document.getElementById('scheduleTimeContainer').classList.add('hidden');
            }
        });

        // Initialize event listeners for nav links
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.nav-link[data-section]').forEach(link => {
                link.addEventListener('click', function() {
                    const section = this.getAttribute('data-section');
                    switchSection(section);
                });
            });
        });
    </script>
</body>
</html>
