<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> thống qu<PERSON>n lý email</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Roboto', sans-serif; background-color: #f8f9fa; }
        .login-container { max-width: 400px; margin: 100px auto; padding: 20px; background-color: #fff; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); }
        .user-container { display: flex; min-height: 100vh; }
        .sidebar { width: 250px; background-color: #fff; border-right: 1px solid #dee2e6; padding: 20px; }
        .sidebar .nav-link { color: #495057; padding: 10px; border-radius: 5px; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { background-color: #e9ecef; color: #007bff; }
        .main-content { flex: 1; padding: 20px; }
        .content-section { background: #fff; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); padding: 20px; }
        .user-profile { display: flex; align-items: center; padding: 10px; margin-bottom: 20px; border-bottom: 1px solid #dee2e6; }
        .user-profile img { width: 40px; height: 40px; border-radius: 50%; margin-right: 10px; }
        .user-profile .user-info { flex: 1; }
        .user-profile .user-info h6 { margin: 0; }
        .user-profile .user-info small { color: #6c757d; }
        .btn-primary { background-color: #007bff; border: none; }
        .btn-secondary { background-color: #6c757d; border: none; }
        .btn-success { background-color: #28a745; border: none; }
        .btn-danger { background-color: #dc3545; border: none; }
        .btn-warning { background-color: #ffc107; border: none; }
        .error-message { color: #dc3545; margin-top: 5px; font-size: 14px; }
        .hidden { display: none; }
        .sub-menu {
            padding-left: 20px;
            margin-bottom: 10px;
        }
        
        .sub-menu .nav-link {
            padding: 5px 10px;
            font-size: 0.9rem;
        }
        
        .nav-item .nav-link[data-section="email-management"] {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="login-screen" class="login-container">
        <h4 class="text-center mb-4">Đăng nhập</h4>
        <div class="mb-3">
            <label for="loginEmail" class="form-label">Email</label>
            <input type="email" class="form-control" id="loginEmail" placeholder="Nhập email của bạn">
            <div id="emailError" class="error-message hidden">Email không hợp lệ</div>
        </div>
        <div class="mb-3">
            <label for="loginPassword" class="form-label">Mật khẩu</label>
            <input type="password" class="form-control" id="loginPassword" placeholder="Nhập mật khẩu">
            <div id="passwordError" class="error-message hidden">Mật khẩu không đúng</div>
        </div>
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="rememberMe">
            <label class="form-check-label" for="rememberMe">Ghi nhớ đăng nhập</label>
        </div>
        <button class="btn btn-primary w-100" onclick="login()">Đăng nhập</button>
        <div class="text-center mt-3">
            <a href="#" onclick="showForgotPassword(); return false;">Quên mật khẩu?</a>
        </div>
        <div id="loginError" class="error-message text-center mt-3 hidden">Email hoặc mật khẩu không đúng</div>
    </div>

    <!-- Forgot Password Screen -->
    <div id="forgot-password-screen" class="login-container hidden">
        <h4 class="text-center mb-4">Khôi phục mật khẩu</h4>
        <div class="mb-3">
            <label for="forgotEmail" class="form-label">Email</label>
            <input type="email" class="form-control" id="forgotEmail" placeholder="Nhập email của bạn">
            <div id="forgotEmailError" class="error-message hidden">Email không hợp lệ</div>
        </div>
        <button class="btn btn-primary w-100" onclick="resetPassword()">Gửi yêu cầu khôi phục</button>
        <div class="text-center mt-3">
            <a href="#" onclick="showLogin(); return false;">Quay lại đăng nhập</a>
        </div>
        <div id="resetSuccess" class="alert alert-success mt-3 hidden">
            Yêu cầu khôi phục mật khẩu đã được gửi. Vui lòng kiểm tra email của bạn.
        </div>
    </div>

    <!-- User Dashboard -->
    <div id="user-dashboard" class="user-container hidden">
        <div class="sidebar">
            <div class="user-profile" onclick="showUserProfile()">
                <img src="https://www.svgrepo.com/show/98289/user.svg" alt="User Avatar">
                <div class="user-info">
                    <h6 id="userFullName">Nguyễn Văn A</h6>
                    <small id="userEmail"><EMAIL></small>
                </div>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" data-section="email-management">Quản lý thư điện tử</a>
                    <ul class="nav flex-column sub-menu">
                        <li class="nav-item"><a class="nav-link active" data-section="compose-email">Tạo mới thư</a></li>
                        <li class="nav-item"><a class="nav-link" data-section="send-email">Gửi thư</a></li>
                        <li class="nav-item"><a class="nav-link" data-section="sent-emails">Xem lại thư đã gửi</a></li>
                    </ul>
                </li>
                <li class="nav-item"><a class="nav-link text-danger" onclick="logout()">Đăng xuất</a></li>
            </ul>
        </div>
        <div class="main-content">
            <!-- Compose Email Section -->
            <div class="content-section" id="compose-email-section">
                <h5>Soạn thư mới</h5>
                <div class="mb-3">
                    <label for="emailTo" class="form-label">Người nhận</label>
                    <div class="input-group">
                        <input type="email" class="form-control" id="emailTo" placeholder="Nhập email người nhận">
                        <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#importContactsModal">
                            <i class="fas fa-users"></i> Import danh sách
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="emailSubject" class="form-label">Tiêu đề</label>
                    <input type="text" class="form-control" id="emailSubject" placeholder="Nhập tiêu đề email">
                </div>
                <div class="mb-3">
                    <label for="emailContent" class="form-label">Nội dung</label>
                    <div class="email-toolbar border rounded p-1 mb-1 bg-light">
                        <button class="btn btn-sm btn-light" title="Đính kèm tệp"><i class="fas fa-paperclip"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn liên kết"><i class="fas fa-link"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn biểu tượng cảm xúc"><i class="far fa-smile"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn hình ảnh"><i class="far fa-image"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn biểu đồ"><i class="fas fa-chart-pie"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn bảng"><i class="fas fa-table"></i></button>
                        <button class="btn btn-sm btn-light" title="Khóa nội dung"><i class="fas fa-lock"></i></button>
                        <button class="btn btn-sm btn-light" title="Chỉnh sửa"><i class="fas fa-pen"></i></button>
                        <button class="btn btn-sm btn-light" title="Lịch"><i class="far fa-calendar"></i></button>
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="moreToolsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="moreToolsDropdown">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-font"></i> Định dạng văn bản</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-code"></i> Chèn HTML</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-signature"></i> Chèn chữ ký</a></li>
                            </ul>
                        </div>
                    </div>
                    <textarea class="form-control" id="emailContent" rows="10" placeholder="Nhập nội dung email"></textarea>
                </div>
                <div class="mb-3">
                    <label for="emailTemplate" class="form-label">Chọn template</label>
                    <select class="form-control" id="emailTemplate">
                        <option value="">-- Không sử dụng template --</option>
                        <option value="template1">Template 1</option>
                        <option value="template2">Template 2</option>
                    </select>
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="scheduleEmail">
                    <label class="form-check-label" for="scheduleEmail">Hẹn giờ gửi</label>
                </div>
                <div class="mb-3 hidden" id="scheduleTimeContainer">
                    <label for="scheduleTime" class="form-label">Thời gian gửi</label>
                    <input type="datetime-local" class="form-control" id="scheduleTime">
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" onclick="sendEmail()">Gửi</button>
                    <button class="btn btn-secondary" onclick="saveAsDraft()">Lưu bản nháp</button>
                </div>
            </div>

            <!-- Sent Emails Section -->
            <div class="content-section d-none" id="sent-emails-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Thư đã gửi</h5>
                    <div class="search-container">
                        <input type="text" class="form-control" id="searchSentEmails" placeholder="Tìm kiếm..." onkeyup="searchSentEmails()">
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Người nhận</th>
                            <th>Tiêu đề</th>
                            <th>Thời gian gửi</th>
                            <th>Trạng thái</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody id="sent-emails-list">
                        <!-- Sent emails will be populated here -->
                    </tbody>
                </table>
            </div>

            <!-- Draft Emails Section -->
            <div class="content-section d-none" id="draft-emails-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Bản nháp</h5>
                    <div class="search-container">
                        <input type="text" class="form-control" id="searchDraftEmails" placeholder="Tìm kiếm..." onkeyup="searchDraftEmails()">
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Người nhận</th>
                            <th>Tiêu đề</th>
                            <th>Thời gian lưu</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody id="draft-emails-list">
                        <!-- Draft emails will be populated here -->
                    </tbody>
                </table>
            </div>

            <!-- Scheduled Emails Section -->
            <div class="content-section d-none" id="scheduled-emails-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Thư đã hẹn giờ</h5>
                    <div class="search-container">
                        <input type="text" class="form-control" id="searchScheduledEmails" placeholder="Tìm kiếm..." onkeyup="searchScheduledEmails()">
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Người nhận</th>
                            <th>Tiêu đề</th>
                            <th>Thời gian hẹn gửi</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody id="scheduled-emails-list">
                        <!-- Scheduled emails will be populated here -->
                    </tbody>
                </table>
            </div>

            <!-- Email Templates Section -->
            <div class="content-section d-none" id="email-templates-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Quản lý template email</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="searchTemplates" placeholder="Tìm kiếm..." onkeyup="searchTemplates()">
                        <button class="btn btn-primary" onclick="showCreateTemplateForm()">Tạo mới template</button>
                    </div>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Tên template</th>
                            <th>Trạng thái</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody id="email-templates-list">
                        <!-- Email templates will be populated here -->
                    </tbody>
                </table>
            </div>

            <!-- Template Editor Section -->
            <div class="content-section d-none" id="template-editor-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 id="template-editor-title">Tạo mới template</h5>
                    <button class="btn btn-secondary" onclick="switchSection('email-templates')">Quay lại</button>
                </div>
                <div class="mb-3">
                    <label for="templateName" class="form-label">Tên template</label>
                    <input type="text" class="form-control" id="templateName" placeholder="Nhập tên template">
                </div>
                <div class="mb-3">
                    <label for="templateContent" class="form-label">Nội dung</label>
                    <div class="email-toolbar border rounded p-1 mb-1 bg-light">
                        <button class="btn btn-sm btn-light" title="Đính kèm tệp"><i class="fas fa-paperclip"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn liên kết"><i class="fas fa-link"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn biểu tượng cảm xúc"><i class="far fa-smile"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn hình ảnh"><i class="far fa-image"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn biểu đồ"><i class="fas fa-chart-pie"></i></button>
                        <button class="btn btn-sm btn-light" title="Chèn bảng"><i class="fas fa-table"></i></button>
                        <button class="btn btn-sm btn-light" title="Khóa nội dung"><i class="fas fa-lock"></i></button>
                        <button class="btn btn-sm btn-light" title="Chỉnh sửa"><i class="fas fa-pen"></i></button>
                        <button class="btn btn-sm btn-light" title="Lịch"><i class="far fa-calendar"></i></button>
                        <div class="dropdown d-inline-block">
                            <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="templateToolsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="templateToolsDropdown">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-font"></i> Định dạng văn bản</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-code"></i> Chèn HTML</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-signature"></i> Chèn chữ ký</a></li>
                            </ul>
                        </div>
                    </div>
                    <textarea class="form-control" id="templateContent" rows="10" placeholder="Nhập nội dung template"></textarea>
                </div>
                <div class="mb-3" id="templateStatusContainer">
                    <label for="templateStatus" class="form-label">Trạng thái</label>
                    <select class="form-control" id="templateStatus">
                        <option value="Bản nháp">Bản nháp</option>
                        <option value="Active">Active</option>
                        <option value="Deactive">Deactive</option>
                    </select>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" id="saveTemplateBtn" onclick="saveTemplate()">Lưu template</button>
                    <button class="btn btn-secondary" onclick="switchSection('email-templates')">Hủy</button>
                </div>
            </div>

            <!-- API Management Section -->
            <div class="content-section d-none" id="api-management-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Quản lý API</h5>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="searchApiKeys" placeholder="Tìm kiếm..." onkeyup="searchApiKeys()">
                        <button class="btn btn-primary" onclick="showCreateApiKeyForm()">Tạo API Key mới</button>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>API Keys của bạn</h6>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Tên</th>
                                <th>API Key</th>
                                <th>Ngày tạo</th>
                                <th>Trạng thái</th>
                                <th>Hành động</th>
                            </tr>
                        </thead>
                        <tbody id="api-keys-list">
                            <!-- API keys will be populated here -->
                        </tbody>
                    </table>
                </div>
                
                <div class="mb-3">
                    <h6>Tài liệu API</h6>
                    <p>Sử dụng API key của bạn để gửi email thông qua API. Dưới đây là các endpoint có sẵn:</p>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Endpoint</th>
                                    <th>Phương thức</th>
                                    <th>Mô tả</th>
                                    <th>Tham số</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>/api/send-email</code></td>
                                    <td>POST</td>
                                    <td>Gửi email</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li><code>to</code>: Email người nhận (bắt buộc)</li>
                                            <li><code>subject</code>: Tiêu đề email (bắt buộc)</li>
                                            <li><code>content</code>: Nội dung email (bắt buộc)</li>
                                            <li><code>scheduled_time</code>: Thời gian gửi (tùy chọn)</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/emails</code></td>
                                    <td>GET</td>
                                    <td>Lấy danh sách email đã gửi</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li><code>page</code>: Số trang (mặc định: 1)</li>
                                            <li><code>limit</code>: Số lượng kết quả mỗi trang (mặc định: 10)</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><code>/api/templates</code></td>
                                    <td>GET</td>
                                    <td>Lấy danh sách template</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li><code>status</code>: Lọc theo trạng thái (tùy chọn)</li>
                                        </ul>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- User Profile Section -->
            <div class="content-section d-none" id="user-profile-section">
                <h5>Thông tin tài khoản</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="profileFullName" class="form-label">Họ tên</label>
                        <input type="text" class="form-control" id="profileFullName" value="Nguyễn Văn A">
                    </div>
                    <div class="col-md-6">
                        <label for="profileEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="profileEmail" value="<EMAIL>" readonly>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="profilePhone" class="form-label">Số điện thoại</label>
                        <input type="tel" class="form-control" id="profilePhone" value="0123456789">
                    </div>
                    <div class="col-md-6">
                        <label for="profileBirthday" class="form-label">Ngày sinh</label>
                        <input type="date" class="form-control" id="profileBirthday" value="1990-01-01">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="profileAddress" class="form-label">Địa chỉ</label>
                    <textarea class="form-control" id="profileAddress" rows="3">123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh</textarea>
                </div>
                <div class="mb-3">
                    <label for="profileWorker" class="form-label">Worker được gắn</label>
                    <input type="text" class="form-control" id="profileWorker" value="Worker 1" readonly>
                </div>
                
                <!-- Phần đổi mật khẩu -->
                <div class="mt-4 mb-3">
                    <div class="row align-items-center mb-3">
                        <div class="col-4">
                            <h5 class="mb-0">Đổi mật khẩu</h5>
                        </div>
                        <div class="col-4 text-center">
                            <button class="btn btn-warning" onclick="changePassword()">Đổi mật khẩu</button>
                        </div>
                        <div class="col-4"></div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="currentPassword" class="form-label">Mật khẩu hiện tại</label>
                            <input type="password" class="form-control" id="currentPassword" placeholder="Nhập mật khẩu hiện tại">
                            <div id="currentPasswordError" class="error-message hidden">Mật khẩu hiện tại không đúng</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="newPassword" class="form-label">Mật khẩu mới</label>
                            <input type="password" class="form-control" id="newPassword" placeholder="Nhập mật khẩu mới">
                            <div id="newPasswordError" class="error-message hidden">Mật khẩu mới phải có ít nhất 6 ký tự</div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="confirmPassword" class="form-label">Xác nhận mật khẩu mới</label>
                            <input type="password" class="form-control" id="confirmPassword" placeholder="Nhập lại mật khẩu mới">
                            <div id="confirmPasswordError" class="error-message hidden">Mật khẩu xác nhận không khớp</div>
                        </div>
                    </div>
                    <div id="passwordChangeSuccess" class="alert alert-success mt-2 hidden">Mật khẩu đã được thay đổi thành công!</div>
                </div>
                
                <button class="btn btn-primary mt-3" onclick="saveProfile()">Lưu thay đổi</button>
            </div>
        </div>
    </div>

    <!-- Import Contacts Modal -->
    <div class="modal fade" id="importContactsModal" tabindex="-1" aria-labelledby="importContactsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importContactsModalLabel">Import danh sách người nhận</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importMethod" class="form-label">Chọn phương thức import</label>
                        <select class="form-control" id="importMethod" onchange="toggleImportMethod()">
                            <option value="file">Tải lên file CSV/Excel</option>
                            <option value="paste">Dán danh sách email</option>
                            <option value="contacts">Chọn từ danh bạ</option>
                        </select>
                    </div>
                    
                    <div id="fileImportContainer">
                        <div class="mb-3">
                            <label for="contactsFile" class="form-label">Chọn file CSV hoặc Excel</label>
                            <input type="file" class="form-control" id="contactsFile" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <div class="form-text">File phải có cột chứa địa chỉ email. Kích thước tối đa: 5MB.</div>
                    </div>
                    
                    <div id="pasteImportContainer" class="d-none">
                        <div class="mb-3">
                            <label for="pastedEmails" class="form-label">Dán danh sách email (mỗi email một dòng)</label>
                            <textarea class="form-control" id="pastedEmails" rows="5" placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                        </div>
                    </div>
                    
                    <div id="contactsImportContainer" class="d-none">
                        <div class="mb-3">
                            <label class="form-label">Chọn từ danh bạ</label>
                            <div class="list-group" style="max-height: 200px; overflow-y: auto;">
                                <label class="list-group-item">
                                    <input class="form-check-input me-1" type="checkbox" value="<EMAIL>">
                                    Nguyễn Văn B (<EMAIL>)
                                </label>
                                <label class="list-group-item">
                                    <input class="form-check-input me-1" type="checkbox" value="<EMAIL>">
                                    Trần Thị C (<EMAIL>)
                                </label>
                                <label class="list-group-item">
                                    <input class="form-check-input me-1" type="checkbox" value="<EMAIL>">
                                    Lê Văn D (<EMAIL>)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-primary" onclick="importContacts()">Import</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create API Key Modal -->
    <div class="modal fade" id="createApiKeyModal" tabindex="-1" aria-labelledby="createApiKeyModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createApiKeyModalLabel">Tạo API Key mới</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="apiKeyName" class="form-label">Tên API Key</label>
                        <input type="text" class="form-control" id="apiKeyName" placeholder="Nhập tên để nhận diện API key này">
                    </div>
                    <div class="mb-3">
                        <label for="apiKeyPermissions" class="form-label">Quyền truy cập</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="send_email" id="permSendEmail" checked>
                            <label class="form-check-label" for="permSendEmail">
                                Gửi email
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="read_emails" id="permReadEmails" checked>
                            <label class="form-check-label" for="permReadEmails">
                                Đọc danh sách email
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="read_templates" id="permReadTemplates" checked>
                            <label class="form-check-label" for="permReadTemplates">
                                Đọc danh sách template
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-primary" onclick="createApiKey()">Tạo</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit API Key Modal -->
    <div class="modal fade" id="editApiKeyModal" tabindex="-1" aria-labelledby="editApiKeyModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editApiKeyModalLabel">Chỉnh sửa API Key</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editApiKeyName" class="form-label">Tên API Key</label>
                        <input type="text" class="form-control" id="editApiKeyName">
                    </div>
                    <div class="mb-3">
                        <label for="editApiKeyStatus" class="form-label">Trạng thái</label>
                        <select class="form-control" id="editApiKeyStatus">
                            <option value="Active">Active</option>
                            <option value="Inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quyền truy cập</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="send_email" id="editPermSendEmail">
                            <label class="form-check-label" for="editPermSendEmail">
                                Gửi email
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="read_emails" id="editPermReadEmails">
                            <label class="form-check-label" for="editPermReadEmails">
                                Đọc danh sách email
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="read_templates" id="editPermReadTemplates">
                            <label class="form-check-label" for="editPermReadTemplates">
                                Đọc danh sách template
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-primary" id="updateApiKeyBtn">Lưu thay đổi</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample data
        let currentUser = null;
        const users = {
            "<EMAIL>": {
                password: "password123",
                fullName: "Nguyễn Văn A",
                phone: "0123456789",
                birthday: "1990-01-01",
                address: "123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh",
                worker: "Worker 1"
            }
        };
        
        let sentEmails = [];
        let draftEmails = [];
        let scheduledEmails = [];
        let emailTemplates = [
            { id: 1, name: "Template 1", status: "Active" },
            { id: 2, name: "Template 2", status: "Bản nháp" }
        ];

        // Sample data for API keys
        let apiKeys = [
            {
                id: 1,
                name: "Default API Key",
                key: "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
                created: "01/01/2023",
                status: "Active",
                permissions: ["send_email", "read_emails", "read_templates"]
            }
        ];

        // Login function
        function login() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            // Reset error messages
            document.getElementById('emailError').classList.add('hidden');
            document.getElementById('passwordError').classList.add('hidden');
            document.getElementById('loginError').classList.add('hidden');
            
            // Validate email format
            if (!email || !email.includes('@')) {
                document.getElementById('emailError').classList.remove('hidden');
                return;
            }
            
            // Check if user exists
            if (users[email]) {
                // Check password
                if (users[email].password === password) {
                    currentUser = {
                        email: email,
                        ...users[email]
                    };
                    
                    // Update user info in dashboard
                    document.getElementById('userFullName').textContent = currentUser.fullName;
                    document.getElementById('userEmail').textContent = currentUser.email;
                    
                    // Show dashboard, hide login
                    document.getElementById('login-screen').classList.add('hidden');
                    document.getElementById('user-dashboard').classList.remove('hidden');
                    
                    // Load initial data
                    loadSentEmails();
                    loadDraftEmails();
                    loadScheduledEmails();
                    loadEmailTemplates();
                } else {
                    document.getElementById('passwordError').classList.remove('hidden');
                }
            } else {
                document.getElementById('loginError').classList.remove('hidden');
            }
        }
        
        // Logout function
        function logout() {
            currentUser = null;
            document.getElementById('login-screen').classList.remove('hidden');
            document.getElementById('user-dashboard').classList.add('hidden');
            document.getElementById('loginEmail').value = '';
            document.getElementById('loginPassword').value = '';
        }
        
        // Switch between sections
        function switchSection(section) {
            // Hide all content sections
            document.querySelectorAll('.main-content .content-section').forEach(sec => sec.classList.add('d-none'));
            
            // Remove active class from all nav links
            document.querySelectorAll('.sidebar .nav-link').forEach(link => link.classList.remove('active'));
            
            // Show selected section
            document.getElementById(`${section}-section`).classList.remove('d-none');
            
            // Add active class to selected nav link
            const navLink = document.querySelector(`.nav-link[data-section="${section}"]`);
            if (navLink) navLink.classList.add('active');
            
            // Special handling for send-email section
            if (section === 'send-email') {
                // Populate send email preview with data from compose form
                document.getElementById('sendEmailTo').textContent = document.getElementById('emailTo').value || 'Chưa có người nhận';
                document.getElementById('sendEmailSubject').textContent = document.getElementById('emailSubject').value || 'Chưa có tiêu đề';
                document.getElementById('sendEmailPreview').innerHTML = document.getElementById('emailContent').value || 'Chưa có nội dung';
            }
            
            // Load data based on section
            if (section === 'sent-emails') {
                loadSentEmails();
            }
        }
        
        // Show user profile
        function showUserProfile() {
            switchSection('user-profile');
            
            // Fill profile form with user data
            document.getElementById('profileFullName').value = currentUser.fullName;
            document.getElementById('profileEmail').value = currentUser.email;
            document.getElementById('profilePhone').value = currentUser.phone;
            document.getElementById('profileBirthday').value = currentUser.birthday;
            document.getElementById('profileAddress').value = currentUser.address;
            document.getElementById('profileWorker').value = currentUser.worker;
        }
        
        // Save profile changes
        function saveProfile() {
            const fullName = document.getElementById('profileFullName').value;
            const phone = document.getElementById('profilePhone').value;
            const birthday = document.getElementById('profileBirthday').value;
            const address = document.getElementById('profileAddress').value;
            
            // Update user data
            currentUser.fullName = fullName;
            currentUser.phone = phone;
            currentUser.birthday = birthday;
            currentUser.address = address;
            
            // Update in users object
            users[currentUser.email] = {
                password: currentUser.password,
                fullName,
                phone,
                birthday,
                address,
                worker: currentUser.worker
            };
            
            // Update displayed name
            document.getElementById('userFullName').textContent = fullName;
            
            alert('Thông tin tài khoản đã được cập nhật thành công!');
        }
        
        // Load sent emails
        function loadSentEmails() {
            document.getElementById('searchSentEmails').value = '';
            searchSentEmails();
        }
        
        // Load draft emails
        function loadDraftEmails() {
            document.getElementById('searchDraftEmails').value = '';
            searchDraftEmails();
        }
        
        // Load scheduled emails
        function loadScheduledEmails() {
            document.getElementById('searchScheduledEmails').value = '';
            searchScheduledEmails();
        }
        
        // Load email templates
        function loadEmailTemplates() {
            document.getElementById('searchTemplates').value = '';
            searchTemplates();
        }
        
        let currentEditingTemplateId = null;

        // Show create template form
        function showCreateTemplateForm() {
            // Reset form
            document.getElementById('template-editor-title').textContent = 'Tạo mới template';
            document.getElementById('templateName').value = '';
            document.getElementById('templateContent').value = '';
            document.getElementById('templateStatusContainer').classList.add('d-none'); // Hide status for new template
            
            // Change save button action
            document.getElementById('saveTemplateBtn').setAttribute('onclick', 'createTemplate()');
            
            // Switch to template editor section
            currentEditingTemplateId = null;
            switchSection('template-editor');
        }
        
        // Create a new template
        function createTemplate() {
            const name = document.getElementById('templateName').value;
            const content = document.getElementById('templateContent').value;
            
            if (!name || !content) {
                alert('Vui lòng nhập đầy đủ thông tin!');
                return;
            }
            
            const newId = emailTemplates.length > 0 ? Math.max(...emailTemplates.map(t => t.id)) + 1 : 1;
            
            emailTemplates.push({
                id: newId,
                name: name,
                content: content,
                status: 'Bản nháp'
            });
            
            alert('Template đã được tạo thành công!');
            switchSection('email-templates');
            loadEmailTemplates();
        }
        
        // Edit template
        function editTemplate(id) {
            const template = emailTemplates.find(t => t.id === id);
            if (!template) return;
            
            // Fill the form with template data
            document.getElementById('template-editor-title').textContent = 'Chỉnh sửa template';
            document.getElementById('templateName').value = template.name;
            document.getElementById('templateContent').value = template.content || '';
            
            // Show status dropdown for editing
            document.getElementById('templateStatusContainer').classList.remove('d-none');
            document.getElementById('templateStatus').value = template.status;
            
            // Change save button action
            document.getElementById('saveTemplateBtn').setAttribute('onclick', 'updateTemplate()');
            
            // Set current editing template ID
            currentEditingTemplateId = id;
            
            // Switch to template editor section
            switchSection('template-editor');
        }
        
        // Update template
        function updateTemplate() {
            if (currentEditingTemplateId === null) return;
            
            const name = document.getElementById('templateName').value;
            const content = document.getElementById('templateContent').value;
            const status = document.getElementById('templateStatus').value;
            
            if (!name || !content) {
                alert('Vui lòng nhập đầy đủ thông tin!');
                return;
            }
            
            // Update the template
            const templateIndex = emailTemplates.findIndex(t => t.id === currentEditingTemplateId);
            if (templateIndex !== -1) {
                emailTemplates[templateIndex] = {
                    ...emailTemplates[templateIndex],
                    name,
                    content,
                    status
                };
            }
            
            alert('Template đã được cập nhật thành công!');
            switchSection('email-templates');
            loadEmailTemplates();
        }
        
        // Send email function
        function sendEmail() {
            const to = document.getElementById('emailTo').value;
            const subject = document.getElementById('emailSubject').value;
            const content = document.getElementById('emailContent').value;
            const isScheduled = document.getElementById('scheduleEmail').checked;
            
            if (!to || !subject || !content) {
                alert('Vui lòng nhập đầy đủ thông tin email!');
                return;
            }
            
            if (isScheduled) {
                const scheduleTime = document.getElementById('scheduleTime').value;
                if (!scheduleTime) {
                    alert('Vui lòng chọn thời gian gửi!');
                    return;
                }
                
                // Format the datetime for display
                const formattedTime = new Date(scheduleTime).toLocaleString('vi-VN');
                
                // Add to scheduled emails
                const newId = scheduledEmails.length > 0 ? Math.max(...scheduledEmails.map(e => e.id)) + 1 : 1;
                scheduledEmails.push({
                    id: newId,
                    to: to,
                    subject: subject,
                    content: content,
                    scheduledTime: formattedTime
                });
                
                alert('Email đã được lên lịch gửi!');
                loadScheduledEmails();
            } else {
                // Add to sent emails
                const now = new Date().toLocaleString('vi-VN');
                const newId = sentEmails.length > 0 ? Math.max(...sentEmails.map(e => e.id)) + 1 : 1;
                sentEmails.push({
                    id: newId,
                    to: to,
                    subject: subject,
                    content: content,
                    sentTime: now,
                    status: 'Đã gửi'
                });
                
                alert('Email đã được gửi thành công!');
                loadSentEmails();
            }
            
            // Clear the form
            document.getElementById('emailTo').value = '';
            document.getElementById('emailSubject').value = '';
            document.getElementById('emailContent').value = '';
            document.getElementById('scheduleEmail').checked = false;
            document.getElementById('scheduleTimeContainer').classList.add('hidden');
        }
        
        // Save as draft function
        function saveAsDraft() {
            const to = document.getElementById('emailTo').value;
            const subject = document.getElementById('emailSubject').value;
            const content = document.getElementById('emailContent').value;
            
            if (!to && !subject && !content) {
                alert('Không có nội dung để lưu!');
                return;
            }
            
            // Add to drafts
            const now = new Date().toLocaleString('vi-VN');
            const newId = draftEmails.length > 0 ? Math.max(...draftEmails.map(e => e.id)) + 1 : 1;
            draftEmails.push({
                id: newId,
                to: to || '(Chưa có người nhận)',
                subject: subject || '(Chưa có tiêu đề)',
                content: content,
                savedTime: now
            });
            
            alert('Email đã được lưu vào bản nháp!');
            loadDraftEmails();
            
            // Clear the form
            document.getElementById('emailTo').value = '';
            document.getElementById('emailSubject').value = '';
            document.getElementById('emailContent').value = '';
        }
        
        // Toggle schedule time container
        document.getElementById('scheduleEmail').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('scheduleTimeContainer').classList.remove('hidden');
            } else {
                document.getElementById('scheduleTimeContainer').classList.add('hidden');
            }
        });
        
        // Edit draft email
        function editDraft(id) {
            const draft = draftEmails.find(d => d.id === id);
            if (!draft) return;
            
            // Fill the compose form with draft data
            document.getElementById('emailTo').value = draft.to !== '(Chưa có người nhận)' ? draft.to : '';
            document.getElementById('emailSubject').value = draft.subject !== '(Chưa có tiêu đề)' ? draft.subject : '';
            document.getElementById('emailContent').value = draft.content || '';
            
            // Remove from drafts
            draftEmails = draftEmails.filter(d => d.id !== id);
            loadDraftEmails();
            
            // Switch to compose section
            switchSection('compose-email');
        }
        
        // Delete draft email
        function deleteDraft(id) {
            if (confirm('Bạn có chắc muốn xóa bản nháp này?')) {
                draftEmails = draftEmails.filter(d => d.id !== id);
                loadDraftEmails();
            }
        }
        
        // Edit scheduled email
        function editScheduled(id) {
            const scheduled = scheduledEmails.find(s => s.id === id);
            if (!scheduled) return;
            
            // Fill the compose form with scheduled data
            document.getElementById('emailTo').value = scheduled.to;
            document.getElementById('emailSubject').value = scheduled.subject;
            document.getElementById('emailContent').value = scheduled.content || '';
            document.getElementById('scheduleEmail').checked = true;
            document.getElementById('scheduleTimeContainer').classList.remove('hidden');
            
            // Try to parse the scheduled time back to input format
            try {
                const scheduledDate = new Date(scheduled.scheduledTime.replace(/(\d+)\/(\d+)\/(\d+)/, '$3-$2-$1'));
                const isoString = scheduledDate.toISOString().slice(0, 16);
                document.getElementById('scheduleTime').value = isoString;
            } catch (e) {
                console.error('Could not parse date:', e);
            }
            
            // Remove from scheduled
            scheduledEmails = scheduledEmails.filter(s => s.id !== id);
            loadScheduledEmails();
            
            // Switch to compose section
            switchSection('compose-email');
        }
        
        // Cancel scheduled email
        function cancelScheduled(id) {
            if (confirm('Bạn có chắc muốn hủy email đã hẹn giờ này?')) {
                scheduledEmails = scheduledEmails.filter(s => s.id !== id);
                loadScheduledEmails();
            }
        }
        
        // Use template
        function useTemplate(id) {
            const template = emailTemplates.find(t => t.id === id);
            if (!template) return;
            
            // Fill the compose form with template content
            document.getElementById('emailContent').value = template.content || '';
            
            // Switch to compose section
            switchSection('compose-email');
        }
        
        // Delete template
        function deleteTemplate(id) {
            if (confirm('Bạn có chắc muốn xóa template này?')) {
                emailTemplates = emailTemplates.filter(t => t.id !== id);
                loadEmailTemplates();
            }
        }
        
        // Generate API key
        function generateApiKey() {
            const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
            document.getElementById('apiKey').value = uuid;
        }
        
        // Copy API key
        function copyApiKey() {
            const apiKey = document.getElementById('apiKey');
            apiKey.select();
            document.execCommand('copy');
            alert('API Key đã được sao chép vào clipboard!');
        }
        
        // Show forgot password screen
        function showForgotPassword() {
            document.getElementById('login-screen').classList.add('hidden');
            document.getElementById('forgot-password-screen').classList.remove('hidden');
            document.getElementById('forgotEmail').value = '';
            document.getElementById('forgotEmailError').classList.add('hidden');
            document.getElementById('resetSuccess').classList.add('hidden');
        }

        // Show login screen
        function showLogin() {
            document.getElementById('forgot-password-screen').classList.add('hidden');
            document.getElementById('login-screen').classList.remove('hidden');
        }

        // Reset password function
        function resetPassword() {
            const email = document.getElementById('forgotEmail').value;
            
            // Reset error messages
            document.getElementById('forgotEmailError').classList.add('hidden');
            document.getElementById('resetSuccess').classList.add('hidden');
            
            // Validate email format
            if (!email || !email.includes('@')) {
                document.getElementById('forgotEmailError').classList.remove('hidden');
                return;
            }
            
            // Check if user exists
            if (users[email]) {
                // In a real application, this would send an email with reset instructions
                // For this demo, we'll just show a success message
                document.getElementById('resetSuccess').classList.remove('hidden');
                
                // Generate a temporary password (in a real app, this would be more secure)
                const tempPassword = 'temp' + Math.random().toString(36).substring(2, 8);
                console.log(`Temporary password for ${email}: ${tempPassword}`);
                
                // Update the user's password
                users[email].password = tempPassword;
            } else {
                // Even if user doesn't exist, show success message for security reasons
                document.getElementById('resetSuccess').classList.remove('hidden');
            }
        }
        
        // Change password function
        function changePassword() {
            // Reset error messages
            document.getElementById('currentPasswordError').classList.add('hidden');
            document.getElementById('newPasswordError').classList.add('hidden');
            document.getElementById('confirmPasswordError').classList.add('hidden');
            document.getElementById('passwordChangeSuccess').classList.add('hidden');
            
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            // Validate current password
            if (currentPassword !== currentUser.password) {
                document.getElementById('currentPasswordError').classList.remove('hidden');
                return;
            }
            
            // Validate new password
            if (newPassword.length < 6) {
                document.getElementById('newPasswordError').classList.remove('hidden');
                return;
            }
            
            // Validate password confirmation
            if (newPassword !== confirmPassword) {
                document.getElementById('confirmPasswordError').classList.remove('hidden');
                return;
            }
            
            // Update password
            currentUser.password = newPassword;
            users[currentUser.email].password = newPassword;
            
            // Show success message
            document.getElementById('passwordChangeSuccess').classList.remove('hidden');
            
            // Clear password fields
            document.getElementById('currentPassword').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        }
        
        // Initialize event listeners for nav links
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.nav-link[data-section]').forEach(link => {
                link.addEventListener('click', function() {
                    const section = this.getAttribute('data-section');
                    switchSection(section);
                });
            });
        });

        // Toggle import method
        function toggleImportMethod() {
            const method = document.getElementById('importMethod').value;
            
            document.getElementById('fileImportContainer').classList.add('d-none');
            document.getElementById('pasteImportContainer').classList.add('d-none');
            document.getElementById('contactsImportContainer').classList.add('d-none');
            
            if (method === 'file') {
                document.getElementById('fileImportContainer').classList.remove('d-none');
            } else if (method === 'paste') {
                document.getElementById('pasteImportContainer').classList.remove('d-none');
            } else if (method === 'contacts') {
                document.getElementById('contactsImportContainer').classList.remove('d-none');
            }
        }

        // Import contacts
        function importContacts() {
            const method = document.getElementById('importMethod').value;
            let emails = [];
            
            if (method === 'file') {
                const fileInput = document.getElementById('contactsFile');
                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('Vui lòng chọn file để import!');
                    return;
                }
                
                const file = fileInput.files[0];
                // Trong ứng dụng thực tế, bạn sẽ cần xử lý file CSV/Excel
                // Đây chỉ là mô phỏng
                emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
                
            } else if (method === 'paste') {
                const pastedText = document.getElementById('pastedEmails').value;
                if (!pastedText.trim()) {
                    alert('Vui lòng nhập danh sách email!');
                    return;
                }
                
                // Tách email theo dòng mới
                emails = pastedText.split('\n')
                    .map(email => email.trim())
                    .filter(email => email && email.includes('@'));
                    
            } else if (method === 'contacts') {
                // Lấy tất cả checkbox đã chọn
                const checkboxes = document.querySelectorAll('#contactsImportContainer input[type="checkbox"]:checked');
                if (checkboxes.length === 0) {
                    alert('Vui lòng chọn ít nhất một liên hệ!');
                    return;
                }
                
                emails = Array.from(checkboxes).map(checkbox => checkbox.value);
            }
            
            if (emails.length === 0) {
                alert('Không tìm thấy email hợp lệ để import!');
                return;
            }
            
            // Thêm email vào trường người nhận
            const currentEmails = document.getElementById('emailTo').value;
            const newEmails = emails.join(', ');
            
            if (currentEmails) {
                document.getElementById('emailTo').value = currentEmails + ', ' + newEmails;
            } else {
                document.getElementById('emailTo').value = newEmails;
            }
            
            // Đóng modal
            bootstrap.Modal.getInstance(document.getElementById('importContactsModal')).hide();
            
            alert(`Đã import ${emails.length} địa chỉ email!`);
        }

        // Search functions for each list
        function searchSentEmails() {
            const searchTerm = document.getElementById('searchSentEmails').value.toLowerCase();
            const tbody = document.getElementById('sent-emails-list');
            tbody.innerHTML = '';
            
            const filteredEmails = sentEmails.filter(email => 
                email.to.toLowerCase().includes(searchTerm) || 
                email.subject.toLowerCase().includes(searchTerm) ||
                (email.content && email.content.toLowerCase().includes(searchTerm))
            );
            
            if (filteredEmails.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" class="text-center">Không tìm thấy kết quả phù hợp</td>';
                tbody.appendChild(row);
            } else {
                filteredEmails.forEach(email => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${email.to}</td>
                        <td>${email.subject}</td>
                        <td>${email.sentTime}</td>
                        <td>${email.status}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewSentEmail(${email.id})">Xem</button>
                            <button class="btn btn-sm btn-warning" onclick="resendEmail(${email.id})">Gửi lại</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        function searchDraftEmails() {
            const searchTerm = document.getElementById('searchDraftEmails').value.toLowerCase();
            const tbody = document.getElementById('draft-emails-list');
            tbody.innerHTML = '';
            
            const filteredEmails = draftEmails.filter(email => 
                email.to.toLowerCase().includes(searchTerm) || 
                email.subject.toLowerCase().includes(searchTerm) ||
                (email.content && email.content.toLowerCase().includes(searchTerm))
            );
            
            if (filteredEmails.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="4" class="text-center">Không tìm thấy kết quả phù hợp</td>';
                tbody.appendChild(row);
            } else {
                filteredEmails.forEach(email => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${email.to}</td>
                        <td>${email.subject}</td>
                        <td>${email.savedTime}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editDraft(${email.id})">Chỉnh sửa</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteDraft(${email.id})">Xóa</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        function searchScheduledEmails() {
            const searchTerm = document.getElementById('searchScheduledEmails').value.toLowerCase();
            const tbody = document.getElementById('scheduled-emails-list');
            tbody.innerHTML = '';
            
            const filteredEmails = scheduledEmails.filter(email => 
                email.to.toLowerCase().includes(searchTerm) || 
                email.subject.toLowerCase().includes(searchTerm) ||
                (email.content && email.content.toLowerCase().includes(searchTerm))
            );
            
            if (filteredEmails.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="4" class="text-center">Không tìm thấy kết quả phù hợp</td>';
                tbody.appendChild(row);
            } else {
                filteredEmails.forEach(email => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${email.to}</td>
                        <td>${email.subject}</td>
                        <td>${email.scheduledTime}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editScheduled(${email.id})">Chỉnh sửa</button>
                            <button class="btn btn-sm btn-danger" onclick="cancelScheduled(${email.id})">Hủy</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        function searchTemplates() {
            const searchTerm = document.getElementById('searchTemplates').value.toLowerCase();
            const tbody = document.getElementById('email-templates-list');
            tbody.innerHTML = '';
            
            const filteredTemplates = emailTemplates.filter(template => 
                template.name.toLowerCase().includes(searchTerm) || 
                (template.content && template.content.toLowerCase().includes(searchTerm))
            );
            
            if (filteredTemplates.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="3" class="text-center">Không tìm thấy kết quả phù hợp</td>';
                tbody.appendChild(row);
            } else {
                filteredTemplates.forEach(template => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${template.name}</td>
                        <td>${template.status}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editTemplate(${template.id})">Chỉnh sửa</button>
                            <button class="btn btn-sm btn-info" onclick="useTemplate(${template.id})">Sử dụng</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteTemplate(${template.id})">Xóa</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        // Load API keys
        function loadApiKeys() {
            document.getElementById('searchApiKeys').value = '';
            searchApiKeys();
        }

        // Search API keys
        function searchApiKeys() {
            const searchTerm = document.getElementById('searchApiKeys').value.toLowerCase();
            const tbody = document.getElementById('api-keys-list');
            tbody.innerHTML = '';
            
            const filteredApiKeys = apiKeys.filter(apiKey => 
                apiKey.name.toLowerCase().includes(searchTerm) || 
                apiKey.key.toLowerCase().includes(searchTerm)
            );
            
            if (filteredApiKeys.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" class="text-center">Không tìm thấy API key nào</td>';
                tbody.appendChild(row);
            } else {
                filteredApiKeys.forEach(apiKey => {
                    const row = document.createElement('tr');
                    // Mask API key for display
                    const maskedKey = apiKey.key.substring(0, 8) + '...' + apiKey.key.substring(apiKey.key.length - 4);
                    
                    row.innerHTML = `
                        <td>${apiKey.name}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <code>${maskedKey}</code>
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyApiKeyToClipboard('${apiKey.key}')">
                                    <i class="far fa-copy"></i>
                                </button>
                            </div>
                        </td>
                        <td>${apiKey.created}</td>
                        <td>
                            <span class="badge ${apiKey.status === 'Active' ? 'bg-success' : 'bg-danger'}">
                                ${apiKey.status}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="showEditApiKeyForm(${apiKey.id})">Chỉnh sửa</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteApiKey(${apiKey.id})">Xóa</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        // Show create API key form
        function showCreateApiKeyForm() {
            // Reset form
            document.getElementById('apiKeyName').value = '';
            document.getElementById('permSendEmail').checked = true;
            document.getElementById('permReadEmails').checked = true;
            document.getElementById('permReadTemplates').checked = true;
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('createApiKeyModal'));
            modal.show();
        }

        // Create new API key
        function createApiKey() {
            const name = document.getElementById('apiKeyName').value;
            
            if (!name) {
                alert('Vui lòng nhập tên cho API key!');
                return;
            }
            
            // Collect permissions
            const permissions = [];
            if (document.getElementById('permSendEmail').checked) permissions.push('send_email');
            if (document.getElementById('permReadEmails').checked) permissions.push('read_emails');
            if (document.getElementById('permReadTemplates').checked) permissions.push('read_templates');
            
            // Generate a new API key
            const newKey = generateUUID();
            
            // Get current date
            const today = new Date();
            const formattedDate = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
            
            // Create new API key object
            const newApiKey = {
                id: apiKeys.length > 0 ? Math.max(...apiKeys.map(k => k.id)) + 1 : 1,
                name: name,
                key: newKey,
                created: formattedDate,
                status: "Active",
                permissions: permissions
            };
            
            // Add to API keys array
            apiKeys.push(newApiKey);
            
            // Hide modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('createApiKeyModal'));
            modal.hide();
            
            // Show success message with the new key
            alert(`API Key đã được tạo thành công!\n\nKey: ${newKey}\n\nHãy lưu lại key này vì bạn sẽ không thể xem lại toàn bộ key sau này.`);
            
            // Reload API keys list
            loadApiKeys();
        }

        // Show edit API key form
        function showEditApiKeyForm(id) {
            const apiKey = apiKeys.find(k => k.id === id);
            if (!apiKey) return;
            
            // Fill form with API key data
            document.getElementById('editApiKeyName').value = apiKey.name;
            document.getElementById('editApiKeyStatus').value = apiKey.status;
            document.getElementById('editPermSendEmail').checked = apiKey.permissions.includes('send_email');
            document.getElementById('editPermReadEmails').checked = apiKey.permissions.includes('read_emails');
            document.getElementById('editPermReadTemplates').checked = apiKey.permissions.includes('read_templates');
            
            // Set up update button
            const updateBtn = document.getElementById('updateApiKeyBtn');
            updateBtn.onclick = function() {
                updateApiKey(id);
            };
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editApiKeyModal'));
            modal.show();
        }

        // Update API key
        function updateApiKey(id) {
            const apiKey = apiKeys.find(k => k.id === id);
            if (!apiKey) return;
            
            const name = document.getElementById('editApiKeyName').value;
            const status = document.getElementById('editApiKeyStatus').value;
            
            if (!name) {
                alert('Vui lòng nhập tên cho API key!');
                return;
            }
            
            // Collect permissions
            const permissions = [];
            if (document.getElementById('editPermSendEmail').checked) permissions.push('send_email');
            if (document.getElementById('editPermReadEmails').checked) permissions.push('read_emails');
            if (document.getElementById('editPermReadTemplates').checked) permissions.push('read_templates');
            
            // Update API key
            apiKey.name = name;
            apiKey.status = status;
            apiKey.permissions = permissions;
            
            // Hide modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('editApiKeyModal'));
            modal.hide();
            
            // Show success message
            alert('API Key đã được cập nhật thành công!');
            
            // Reload API keys list
            loadApiKeys();
        }

        // Delete API key
        function deleteApiKey(id) {
            if (confirm('Bạn có chắc muốn xóa API key này? Các ứng dụng đang sử dụng key này sẽ không thể kết nối được nữa.')) {
                apiKeys = apiKeys.filter(k => k.id !== id);
                loadApiKeys();
                alert('API Key đã được xóa thành công!');
            }
        }

        // Copy API key to clipboard
        function copyApiKeyToClipboard(key) {
            navigator.clipboard.writeText(key).then(function() {
                alert('API Key đã được sao chép vào clipboard!');
            }, function() {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = key;
                textarea.style.position = 'fixed';
                document.body.appendChild(textarea);
                textarea.focus();
                textarea.select();
                try {
                    document.execCommand('copy');
                    alert('API Key đã được sao chép vào clipboard!');
                } catch (err) {
                    console.error('Không thể sao chép: ', err);
                }
                document.body.removeChild(textarea);
            });
        }

        // Generate UUID for API key
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
    </script>
</body>
</html>

